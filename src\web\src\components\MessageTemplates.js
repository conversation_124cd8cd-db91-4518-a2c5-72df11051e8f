import React, { useState, useEffect } from 'react';
import { MessageSquare, Plus, Edit, Trash2, Save, X, Eye, Copy } from 'lucide-react';
import { useSocket } from '../contexts/SocketContext';
import { getApiUrl } from '../config/api';
import toast from 'react-hot-toast';

const MessageTemplates = () => {
  const { socket, isConnected } = useSocket();
  const [templates, setTemplates] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [previewTemplate, setPreviewTemplate] = useState(null);

  const [formData, setFormData] = useState({
    name: '',
    template: '',
    description: '',
    template_type: 'normal',
    is_active: true,
    send_once: false
  });

  const availableVariables = [
    { key: 'username', description: 'Tên người dùng Instagram (@username)' },
    { key: 'content', description: 'Nội dung bình luận' },
    { key: 'price', description: 'Tách số từ bình luận (tối thiểu 2 chữ số - VD: "t100" → "100", "190s" → "190", "a2" → không lấy)' },
    { key: 'timestamp', description: 'Thời gian bình luận' },
    { key: 'shop_name', description: 'Tên shop của bạn' },
    { key: 'product', description: 'Tên sản phẩm (nếu có)' }
  ];

  const [selectedCustomerType, setSelectedCustomerType] = useState('regular');

  const customerTypes = [
    { value: 'regular', label: 'Khách mới', description: 'Template cho khách hàng mới (có thông tin STK)' },
    { value: 'vip', label: 'Khách cũ', description: 'Template cho khách hàng thường xuyên (không cần STK)' }
  ];

  const templateTypes = [
    { value: 'normal', label: 'Template thường', description: 'Sử dụng khi bấm nút In thường' },
    { value: 'backup', label: 'Template dự bị', description: 'Sử dụng khi bấm nút In dự bị' }
  ];

  const defaultTemplates = [
    {
      name: 'print_notification',
      template: 'Chào {{username}}! Cảm ơn bạn đã quan tâm: "{{content}}". Giá {{price}} VNĐ. Chúng tôi sẽ liên hệ với bạn sớm nhất!',
      description: 'Tin nhắn gửi khi in bình luận'
    },
    {
      name: 'order_confirmation',
      template: 'Chúc mừng {{username}} đã chốt đơn {{content}}. Tổng tiền: {{price}} VNĐ. Xin cảm ơn!',
      description: 'Chúc mừng chốt đơn'
    },
    {
      name: 'thank_you',
      template: 'Cảm ơn {{username}} đã quan tâm "{{content}}". Giá {{price}} VNĐ. Shop sẽ liên hệ bạn sớm nhất!',
      description: 'Cảm ơn quan tâm'
    },
    {
      name: 'inquiry_response',
      template: 'Hi {{username}}! Shop đã nhận được yêu cầu "{{content}}" của bạn. Giá {{price}} VNĐ. Vui lòng inbox để được tư vấn chi tiết nhé!',
      description: 'Phản hồi câu hỏi'
    },
    {
      name: 'backup_notification',
      template: 'Hi {{username}}! Shop đã ghi nhận yêu cầu "{{content}}" của bạn vào danh sách dự bị. Nếu có hàng, shop sẽ liên hệ bạn ngay. Cảm ơn bạn đã quan tâm!',
      description: 'Tin nhắn dự bị'
    }
  ];

  useEffect(() => {
    loadTemplates(selectedCustomerType);
  }, [selectedCustomerType]);

  const loadTemplates = async (customerType = null) => {
    setIsLoading(true);
    try {
      const url = customerType ? `/api/message-templates?customer_type=${customerType}` : '/api/message-templates';
      const response = await fetch(getApiUrl(url));
      const data = await response.json();
      if (data.success) {
        setTemplates(data.templates);
      }
    } catch (error) {
      console.error('Failed to load templates:', error);
      toast.error('Lỗi khi tải templates');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveTemplate = async () => {
    if (!formData.name || !formData.template) {
      toast.error('Vui lòng nhập tên và nội dung template');
      return;
    }

    // Check for duplicate name (only for new templates)
    if (!editingTemplate) {
      const existingTemplate = templates.find(t =>
        t.name.toLowerCase() === formData.name.toLowerCase() &&
        t.customer_type === selectedCustomerType
      );

      if (existingTemplate) {
        toast.error(`Template với tên "${formData.name}" đã tồn tại cho loại khách hàng này. Vui lòng chọn tên khác.`);
        return;
      }
    }

    try {
      const url = editingTemplate ? `/api/message-templates/${editingTemplate.id}` : '/api/message-templates';
      const method = editingTemplate ? 'PUT' : 'POST';

      const response = await fetch(getApiUrl(url), {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...formData,
          customer_type: selectedCustomerType,
          template_type: formData.template_type,
          variables: JSON.stringify(extractVariables(formData.template)),
          send_once: formData.send_once
        })
      });

      const data = await response.json();
      if (data.success) {
        toast.success(editingTemplate ? 'Template đã cập nhật' : 'Template đã tạo');
        // Refresh templates for current customer type
        await loadTemplates(selectedCustomerType);
        resetForm();
      } else {
        // Handle specific error messages
        if (data.error && data.error.includes('UNIQUE constraint')) {
          toast.error(`Template với tên "${formData.name}" đã tồn tại cho loại khách hàng này`);
        } else {
          toast.error(data.error || 'Lỗi khi lưu template');
        }
      }
    } catch (error) {
      console.error('Failed to save template:', error);
      toast.error('Lỗi khi lưu template');
    }
  };

  const handleDeleteTemplate = async (templateId) => {
    if (!confirm('Bạn có chắc muốn xóa template này?')) return;

    try {
      const response = await fetch(getApiUrl(`/api/message-templates/${templateId}`), {
        method: 'DELETE'
      });

      const data = await response.json();
      if (data.success) {
        toast.success('Template đã xóa');
        loadTemplates(selectedCustomerType);
      } else {
        toast.error(data.error || 'Lỗi khi xóa template');
      }
    } catch (error) {
      console.error('Failed to delete template:', error);
      toast.error('Lỗi khi xóa template');
    }
  };

  const handleToggleTemplate = async (template) => {
    try {
      const response = await fetch(getApiUrl(`/api/message-templates/${template.id}`), {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: template.name,
          template: template.template,
          description: template.description || '',
          variables: template.variables || '[]',
          customer_type: template.customer_type,
          template_type: template.template_type || 'normal',
          is_active: !template.is_active,
          send_once: template.send_once || false
        })
      });

      const data = await response.json();
      if (data.success) {
        toast.success(template.is_active ? 'Template đã tắt' : 'Template đã bật');
        loadTemplates(selectedCustomerType);

        // Emit socket event to notify other components
        if (socket && isConnected) {
          const eventData = {
            templateId: template.id,
            is_active: !template.is_active,
            customer_type: template.customer_type
          };
          console.log('Emitting template-updated event:', eventData);
          socket.emit('template-updated', eventData);
        } else {
          console.warn('Socket not available for template update notification:', { socket: !!socket, isConnected });
        }

        // Fallback: Also emit a custom event for direct component communication
        window.dispatchEvent(new CustomEvent('template-updated', {
          detail: {
            templateId: template.id,
            is_active: !template.is_active,
            customer_type: template.customer_type
          }
        }));
      } else {
        toast.error(data.error || 'Lỗi khi cập nhật template');
      }
    } catch (error) {
      console.error('Failed to toggle template:', error);
      toast.error('Lỗi khi cập nhật template');
    }
  };

  const extractVariables = (template) => {
    const matches = template.match(/\{\{(\w+)\}\}/g);
    return matches ? matches.map(match => match.replace(/[{}]/g, '')) : [];
  };

  const previewTemplateWithSampleData = (template) => {
    const sampleData = {
      username: 'Adam',
      content: 't100 xanh',
      timestamp: new Date().toLocaleString('vi-VN'),
      shop_name: 'Shop Thời Trang ABC',
      product: 'Áo thun cotton',
      price: '100'
    };

    let preview = template;
    Object.keys(sampleData).forEach(key => {
      const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
      preview = preview.replace(regex, sampleData[key]);
    });

    return preview;
  };

  const resetForm = () => {
    setFormData({
      name: '',
      template: '',
      description: '',
      template_type: 'normal',
      is_active: true,
      send_once: false
    });
    setEditingTemplate(null);
    setShowAddForm(false);
  };

  const startEdit = (template) => {
    setFormData({
      name: template.name,
      template: template.template,
      description: template.description || '',
      template_type: template.template_type || 'normal',
      is_active: template.is_active,
      send_once: template.send_once || false
    });
    setEditingTemplate(template);
    setShowAddForm(true);
  };

  const insertVariable = (variable) => {
    const placeholder = `{{${variable}}}`;
    setFormData(prev => ({
      ...prev,
      template: prev.template + placeholder
    }));
  };

  const handleRecreateDefaults = async () => {
    if (!confirm('Bạn có chắc muốn khôi phục tất cả templates mặc định? Điều này sẽ XÓA TẤT CẢ templates hiện tại và tạo lại từ đầu.')) {
      return;
    }

    try {
      const response = await fetch(getApiUrl('/api/message-templates/recreate-defaults'), {
        method: 'POST'
      });

      const data = await response.json();
      if (data.success) {
        toast.success('Đã khôi phục templates mặc định thành công');
        await loadTemplates(selectedCustomerType);
      } else {
        toast.error(data.error || 'Lỗi khi khôi phục templates mặc định');
      }
    } catch (error) {
      console.error('Failed to recreate default templates:', error);
      toast.error('Lỗi khi khôi phục templates mặc định');
    }
  };



  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0 mb-6">
        <div className="flex items-center space-x-3">
          <MessageSquare className="h-6 w-6 text-blue-600" />
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Templates tin nhắn</h3>
            <p className="text-sm text-gray-500">Quản lý templates cho tin nhắn tự động</p>
          </div>
        </div>

        <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
          <button
            onClick={() => setShowAddForm(true)}
            className="btn-primary btn-sm w-full sm:w-auto"
          >
            <Plus className="h-4 w-4 mr-2" />
            Thêm template
          </button>

          <button
            onClick={handleRecreateDefaults}
            className="btn-secondary btn-sm w-full sm:w-auto"
            title="Tạo lại tất cả templates mặc định"
          >
            <Copy className="h-4 w-4 mr-2" />
            Khôi phục mặc định
          </button>
        </div>
      </div>

      {/* Info about default templates behavior */}
      <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex items-start space-x-2">
          <div className="text-blue-600 mt-0.5">
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="text-sm text-blue-800">
            <p className="font-medium mb-1">💡 Quản lý Templates:</p>
            <ul className="text-xs space-y-1">
              <li>• Bạn có thể xóa bất kỳ template nào, hệ thống sẽ KHÔNG tự động tạo lại</li>
              <li>• Sử dụng nút "Khôi phục mặc định" để tạo lại tất cả templates gốc khi cần</li>
              <li>• Templates được phân loại: Thường (nút In) và Dự bị (nút In dự bị)</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Customer Type Selector */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Loại khách hàng
        </label>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
          {customerTypes.map((type) => (
            <button
              key={type.value}
              onClick={() => setSelectedCustomerType(type.value)}
              className={`p-3 sm:p-4 rounded-lg border-2 transition-colors ${selectedCustomerType === type.value
                ? type.value === 'vip'
                  ? 'border-yellow-500 bg-yellow-50 text-yellow-700'
                  : 'border-blue-500 bg-blue-50 text-blue-700'
                : 'border-gray-200 bg-white text-gray-700 hover:border-gray-300'
                }`}
            >
              <div className="text-left">
                <div className="font-medium text-sm sm:text-base">{type.label}</div>
                <div className="text-xs sm:text-sm opacity-75 mt-1">{type.description}</div>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Add/Edit Form */}
      {showAddForm && (
        <div className="mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
          <h4 className="text-md font-medium text-gray-900 mb-4">
            {editingTemplate ? 'Chỉnh sửa template' : 'Thêm template mới'}
          </h4>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 mb-4">
            <div className="sm:col-span-2 lg:col-span-1">
              <label className="block text-sm font-medium text-gray-700 mb-1 sm:mb-2">
                Tên template
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="VD: print_notification"
                className="input text-sm sm:text-base"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1 sm:mb-2">
                Loại template
              </label>
              <select
                value={formData.template_type}
                onChange={(e) => setFormData(prev => ({ ...prev, template_type: e.target.value }))}
                className="input text-sm sm:text-base"
              >
                {templateTypes.map((type) => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
              <p className="text-xs text-gray-500 mt-1 hidden sm:block">
                {templateTypes.find(t => t.value === formData.template_type)?.description}
              </p>
            </div>

            <div className="sm:col-span-2 lg:col-span-1">
              <label className="block text-sm font-medium text-gray-700 mb-1 sm:mb-2">
                Mô tả
              </label>
              <input
                type="text"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Mô tả template"
                className="input text-sm sm:text-base"
              />
            </div>
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1 sm:mb-2">
              Nội dung template
            </label>
            <textarea
              value={formData.template}
              onChange={(e) => setFormData(prev => ({ ...prev, template: e.target.value }))}
              placeholder="Chào {{username}}! Cảm ơn bạn đã quan tâm: {{content}}. Giá {{price}} VNĐ"
              rows={3}
              className="input text-sm sm:text-base resize-none"
            />
          </div>

          {/* Variables */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1 sm:mb-2">
              Variables có sẵn (chạm để chèn)
            </label>
            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-1 sm:gap-2">
              {availableVariables.map((variable) => (
                <button
                  key={variable.key}
                  onClick={() => insertVariable(variable.key)}
                  className="px-1.5 py-1 sm:px-2 sm:py-1 bg-blue-100 text-blue-700 rounded text-xs hover:bg-blue-200 transition-colors truncate touch-manipulation"
                  title={variable.description}
                >
                  {`{{${variable.key}}}`}
                </button>
              ))}
            </div>
          </div>

          {/* Preview */}
          {formData.template && (
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Xem trước (với dữ liệu mẫu)
              </label>
              <div className="p-3 bg-blue-50 rounded-lg border border-blue-200 text-sm">
                {previewTemplateWithSampleData(formData.template)}
              </div>
            </div>
          )}

          {/* Responsive layout for form controls */}
          <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
            <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-6">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="is_active"
                  checked={formData.is_active}
                  onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.checked }))}
                  className="checkbox"
                />
                <label htmlFor="is_active" className="text-sm text-gray-700">
                  Kích hoạt template
                </label>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="send_once"
                  checked={formData.send_once}
                  onChange={(e) => setFormData(prev => ({ ...prev, send_once: e.target.checked }))}
                  className="checkbox"
                />
                <label htmlFor="send_once" className="text-sm text-gray-700">
                  Chỉ gửi 1 lần đầu
                </label>
              </div>
            </div>

            <div className="flex space-x-2">
              <button
                onClick={resetForm}
                className="btn-secondary btn-sm flex-1 sm:flex-none"
              >
                <X className="h-4 w-4 mr-2" />
                Hủy
              </button>

              <button
                onClick={handleSaveTemplate}
                className="btn-primary btn-sm flex-1 sm:flex-none"
              >
                <Save className="h-4 w-4 mr-2" />
                {editingTemplate ? 'Cập nhật' : 'Lưu'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Templates List */}
      <div className="space-y-6">
        {isLoading ? (
          <div className="text-center py-8">
            <div className="spinner mx-auto mb-2" />
            <p className="text-gray-500">Đang tải templates...</p>
          </div>
        ) : templates.length === 0 ? (
          <div className="text-center py-8">
            <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 mb-4">Chưa có template nào</p>
            <p className="text-gray-400 text-sm">Nhấn "Thêm template" để tạo template đầu tiên</p>
          </div>
        ) : (
          <>
            {/* Normal Templates */}
            {templates.filter(t => !t.template_type || t.template_type === 'normal').length > 0 && (
              <div>
                <h4 className="text-md font-medium text-gray-900 mb-3 flex items-center">
                  <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                  Templates Thường
                </h4>
                <div className="space-y-3">
                  {templates.filter(t => !t.template_type || t.template_type === 'normal').map((template) => (
            <div
              key={template.id}
              className={`p-4 rounded-lg border ${template.is_active ? 'border-green-200 bg-green-50' : 'border-gray-200 bg-gray-50'
                }`}
            >
              <div className="flex flex-col space-y-3 lg:flex-row lg:items-start lg:justify-between lg:space-y-0">
                <div className="flex-1">
                  <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-2 mb-2">
                    <h4 className="font-medium text-gray-900">{template.name}</h4>
                    <div className="flex flex-wrap gap-1">
                      <span className={`px-2 py-1 text-xs rounded-full ${template.customer_type === 'vip'
                        ? 'bg-yellow-100 text-yellow-700'
                        : 'bg-blue-100 text-blue-700'
                        }`}>
                        {template.customer_type === 'vip' ? 'Khách cũ' : 'Khách mới'}
                      </span>
                      <span className={`px-2 py-1 text-xs rounded-full ${template.template_type === 'backup'
                        ? 'bg-orange-100 text-orange-700'
                        : 'bg-green-100 text-green-700'
                        }`}>
                        {template.template_type === 'backup' ? 'Dự bị' : 'Thường'}
                      </span>
                      {template.send_once && (
                        <span className="px-2 py-1 text-xs rounded-full bg-purple-100 text-purple-700">
                          Chỉ 1 lần
                        </span>
                      )}
                    </div>
                  </div>

                  {template.description && (
                    <p className="text-sm text-gray-600 mb-2">{template.description}</p>
                  )}

                  <div className="text-sm text-gray-800 font-mono bg-white p-2 rounded border break-words">
                    {template.template}
                  </div>

                  {template.variables && JSON.parse(template.variables).length > 0 && (
                    <div className="mt-2">
                      <span className="text-xs text-gray-500">Variables: </span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {JSON.parse(template.variables).map((variable, index) => (
                          <span
                            key={`${template.id}-${variable}-${index}`}
                            className="inline-block px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded"
                          >
                            {`{{${variable}}}`}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Responsive action buttons */}
                <div className="flex flex-row space-x-1 sm:space-x-2 lg:flex-col lg:space-x-0 lg:space-y-2 lg:ml-4">
                  {/* Toggle Button */}
                  <button
                    onClick={() => handleToggleTemplate(template)}
                    className={`btn-xs sm:btn-sm px-2 sm:px-3 py-1 rounded-full text-xs font-medium transition-colors flex-1 lg:flex-none touch-manipulation ${template.is_active
                      ? 'bg-green-100 text-green-700 hover:bg-green-200'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    title={template.is_active ? 'Tắt template' : 'Bật template'}
                  >
                    {template.is_active ? 'ON' : 'OFF'}
                  </button>

                  <button
                    onClick={() => setPreviewTemplate(template)}
                    className="btn-secondary btn-xs sm:btn-sm flex-1 lg:flex-none touch-manipulation"
                    title="Xem trước"
                  >
                    <Eye className="h-3 w-3 sm:h-4 sm:w-4 lg:mr-0" />
                    <span className="ml-1 text-xs sm:text-sm lg:hidden">Xem</span>
                  </button>

                  <button
                    onClick={() => startEdit(template)}
                    className="btn-secondary btn-xs sm:btn-sm flex-1 lg:flex-none touch-manipulation"
                    title="Chỉnh sửa"
                  >
                    <Edit className="h-3 w-3 sm:h-4 sm:w-4 lg:mr-0" />
                    <span className="ml-1 text-xs sm:text-sm lg:hidden">Sửa</span>
                  </button>

                  <button
                    onClick={() => handleDeleteTemplate(template.id)}
                    className="btn-danger btn-xs sm:btn-sm flex-1 lg:flex-none touch-manipulation"
                    title="Xóa"
                  >
                    <Trash2 className="h-3 w-3 sm:h-4 sm:w-4 lg:mr-0" />
                    <span className="ml-1 text-xs sm:text-sm lg:hidden">Xóa</span>
                  </button>
                </div>
              </div>
            </div>
                  ))}
                </div>
              </div>
            )}

            {/* Backup Templates */}
            {templates.filter(t => t.template_type === 'backup').length > 0 && (
              <div>
                <h4 className="text-md font-medium text-gray-900 mb-3 flex items-center">
                  <div className="w-3 h-3 bg-orange-500 rounded-full mr-2"></div>
                  Templates Dự bị
                </h4>
                <div className="space-y-3">
                  {templates.filter(t => t.template_type === 'backup').map((template) => (
                    <div
                      key={template.id}
                      className={`p-4 rounded-lg border ${template.is_active ? 'border-green-200 bg-green-50' : 'border-gray-200 bg-gray-50'
                        }`}
                    >
                      <div className="flex flex-col space-y-3 lg:flex-row lg:items-start lg:justify-between lg:space-y-0">
                        <div className="flex-1">
                          <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-2 mb-2">
                            <h4 className="font-medium text-gray-900">{template.name}</h4>
                            <div className="flex flex-wrap gap-1">
                              <span className={`px-2 py-1 text-xs rounded-full ${template.customer_type === 'vip'
                                ? 'bg-yellow-100 text-yellow-700'
                                : 'bg-blue-100 text-blue-700'
                                }`}>
                                {template.customer_type === 'vip' ? 'Khách cũ' : 'Khách mới'}
                              </span>
                              <span className="px-2 py-1 text-xs rounded-full bg-orange-100 text-orange-700">
                                Dự bị
                              </span>
                              {template.send_once && (
                                <span className="px-2 py-1 text-xs rounded-full bg-purple-100 text-purple-700">
                                  Chỉ 1 lần
                                </span>
                              )}
                            </div>
                          </div>

                          {template.description && (
                            <p className="text-sm text-gray-600 mb-2">{template.description}</p>
                          )}

                          <div className="text-sm text-gray-800 font-mono bg-white p-2 rounded border break-words">
                            {template.template}
                          </div>

                          {template.variables && JSON.parse(template.variables).length > 0 && (
                            <div className="mt-2">
                              <span className="text-xs text-gray-500">Variables: </span>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {JSON.parse(template.variables).map((variable, index) => (
                                  <span
                                    key={`${template.id}-${variable}-${index}`}
                                    className="inline-block px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded"
                                  >
                                    {`{{${variable}}}`}
                                  </span>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>

                        {/* Responsive action buttons */}
                        <div className="flex flex-row space-x-1 sm:space-x-2 lg:flex-col lg:space-x-0 lg:space-y-2 lg:ml-4">
                          <button
                            onClick={() => setPreviewTemplate(template)}
                            className="btn-secondary btn-xs sm:btn-sm flex-1 lg:flex-none touch-manipulation"
                            title="Xem trước"
                          >
                            <Eye className="h-3 w-3 sm:h-4 sm:w-4 lg:mr-0" />
                            <span className="ml-1 text-xs sm:text-sm lg:hidden">Xem</span>
                          </button>

                          <button
                            onClick={() => startEdit(template)}
                            className="btn-secondary btn-xs sm:btn-sm flex-1 lg:flex-none touch-manipulation"
                            title="Chỉnh sửa"
                          >
                            <Edit className="h-3 w-3 sm:h-4 sm:w-4 lg:mr-0" />
                            <span className="ml-1 text-xs sm:text-sm lg:hidden">Sửa</span>
                          </button>

                          <button
                            onClick={() => handleToggleTemplate(template)}
                            className={`btn-xs sm:btn-sm px-2 sm:px-3 py-1 rounded-full text-xs font-medium transition-colors flex-1 lg:flex-none touch-manipulation ${template.is_active
                              ? 'bg-green-100 text-green-700 hover:bg-green-200'
                              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                              }`}
                            title={template.is_active ? 'Tắt template' : 'Bật template'}
                          >
                            {template.is_active ? 'ON' : 'OFF'}
                          </button>

                          <button
                            onClick={() => handleDeleteTemplate(template.id)}
                            className="btn-danger btn-xs sm:btn-sm flex-1 lg:flex-none touch-manipulation"
                            title="Xóa template"
                          >
                            <Trash2 className="h-3 w-3 sm:h-4 sm:w-4 lg:mr-0" />
                            <span className="ml-1 text-xs sm:text-sm lg:hidden">Xóa</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {/* Preview Modal */}
      {previewTemplate && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-4 sm:p-6 max-w-lg w-full max-h-[90vh] overflow-y-auto">
            <h3 className="text-lg font-semibold mb-4 break-words">Xem trước: {previewTemplate.name}</h3>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Template gốc:
              </label>
              <div className="p-3 bg-gray-100 rounded text-sm font-mono break-words overflow-x-auto">
                {previewTemplate.template}
              </div>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Kết quả với dữ liệu mẫu:
              </label>
              <div className="p-3 bg-blue-50 rounded text-sm break-words">
                {previewTemplateWithSampleData(previewTemplate.template)}
              </div>
            </div>

            <div className="flex justify-end">
              <button
                onClick={() => setPreviewTemplate(null)}
                className="btn-secondary w-full sm:w-auto"
              >
                Đóng
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MessageTemplates;

import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';

const PriceMappings = () => {
  const [mappings, setMappings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingMapping, setEditingMapping] = useState(null);
  const [formData, setFormData] = useState({
    prefix: '',
    price: '',
    description: '',
    is_active: true
  });

  useEffect(() => {
    fetchMappings();
  }, []);

  const fetchMappings = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/price-mappings');
      const data = await response.json();
      
      if (data.success) {
        setMappings(data.mappings);
      } else {
        throw new Error(data.error || 'Failed to fetch price mappings');
      }
    } catch (error) {
      console.error('Error fetching price mappings:', error);
      toast.error('Lỗi tải danh sách quy đổi giá');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.prefix || !formData.price) {
      toast.error('Vui lòng nhập đầy đủ thông tin');
      return;
    }

    try {
      const url = editingMapping 
        ? `/api/price-mappings/${editingMapping.id}`
        : '/api/price-mappings';
      
      const method = editingMapping ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();
      
      if (data.success) {
        toast.success(editingMapping ? 'Cập nhật thành công' : 'Thêm mới thành công');
        setShowAddForm(false);
        setEditingMapping(null);
        setFormData({ prefix: '', price: '', description: '', is_active: true });
        fetchMappings();
      } else {
        throw new Error(data.error || 'Failed to save price mapping');
      }
    } catch (error) {
      console.error('Error saving price mapping:', error);
      toast.error('Lỗi lưu quy đổi giá');
    }
  };

  const handleEdit = (mapping) => {
    setFormData({
      prefix: mapping.prefix,
      price: mapping.price,
      description: mapping.description || '',
      is_active: mapping.is_active
    });
    setEditingMapping(mapping);
    setShowAddForm(true);
  };

  const handleDelete = async (id) => {
    if (!window.confirm('Bạn có chắc muốn xóa quy đổi giá này?')) {
      return;
    }

    try {
      const response = await fetch(`/api/price-mappings/${id}`, {
        method: 'DELETE',
      });

      const data = await response.json();
      
      if (data.success) {
        toast.success('Xóa thành công');
        fetchMappings();
      } else {
        throw new Error(data.error || 'Failed to delete price mapping');
      }
    } catch (error) {
      console.error('Error deleting price mapping:', error);
      toast.error('Lỗi xóa quy đổi giá');
    }
  };

  const cancelEdit = () => {
    setShowAddForm(false);
    setEditingMapping(null);
    setFormData({ prefix: '', price: '', description: '', is_active: true });
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-bold text-gray-900">Quy Đổi Giá Mã Chốt Đơn</h2>
        <button
          onClick={() => setShowAddForm(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          + Thêm Quy Đổi
        </button>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="font-medium text-blue-900 mb-2">Cách hoạt động:</h3>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Khi có comment chứa mã như "t91", "k15", hệ thống sẽ tìm prefix tương ứng</li>
          <li>• Nếu tìm thấy prefix "t" → giá sẽ là "79k", prefix "k" → giá sẽ là "99k"</li>
          <li>• Biến <code className="bg-blue-100 px-1 rounded">{'{{price}}'}</code> trong template sẽ được thay thế bằng giá đã quy đổi</li>
          <li>• Ví dụ: "t25" → "79k", "k80" → "99k"</li>
        </ul>
      </div>

      {showAddForm && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium mb-4">
            {editingMapping ? 'Chỉnh Sửa Quy Đổi' : 'Thêm Quy Đổi Mới'}
          </h3>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Ký tự đầu (prefix) *
                </label>
                <input
                  type="text"
                  value={formData.prefix}
                  onChange={(e) => setFormData(prev => ({ ...prev, prefix: e.target.value.toLowerCase() }))}
                  placeholder="t, k, s..."
                  className="input"
                  maxLength="5"
                  required
                />
                <p className="text-xs text-gray-500 mt-1">Ví dụ: t, k, s (sẽ match t91, k15, s20...)</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Giá quy đổi *
                </label>
                <input
                  type="text"
                  value={formData.price}
                  onChange={(e) => setFormData(prev => ({ ...prev, price: e.target.value }))}
                  placeholder="79k, 99k, 150k..."
                  className="input"
                  required
                />
                <p className="text-xs text-gray-500 mt-1">Ví dụ: 79k, 99k, 150k</p>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Mô tả
              </label>
              <input
                type="text"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Mô tả quy đổi giá này..."
                className="input"
              />
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="is_active"
                checked={formData.is_active}
                onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.checked }))}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="is_active" className="ml-2 block text-sm text-gray-900">
                Kích hoạt
              </label>
            </div>

            <div className="flex space-x-3">
              <button
                type="submit"
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                {editingMapping ? 'Cập Nhật' : 'Thêm Mới'}
              </button>
              <button
                type="button"
                onClick={cancelEdit}
                className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 transition-colors"
              >
                Hủy
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium">Danh Sách Quy Đổi ({mappings.length})</h3>
        </div>
        
        {mappings.length === 0 ? (
          <div className="p-6 text-center text-gray-500">
            Chưa có quy đổi giá nào. Thêm quy đổi đầu tiên!
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Prefix
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Giá
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Mô tả
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Trạng thái
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Thao tác
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {mappings.map((mapping) => (
                  <tr key={mapping.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {mapping.prefix}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm font-medium text-gray-900">{mapping.price}</span>
                    </td>
                    <td className="px-6 py-4">
                      <span className="text-sm text-gray-500">{mapping.description || '-'}</span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        mapping.is_active 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {mapping.is_active ? 'Kích hoạt' : 'Tắt'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                      <button
                        onClick={() => handleEdit(mapping)}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        Sửa
                      </button>
                      <button
                        onClick={() => handleDelete(mapping.id)}
                        className="text-red-600 hover:text-red-900"
                      >
                        Xóa
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default PriceMappings;

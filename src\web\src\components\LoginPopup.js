import React, { useState, useEffect } from 'react';
import { X, Eye, EyeOff, LogIn, LogOut } from 'lucide-react';
import credentialsService from '../services/credentialsService';

const LoginPopup = ({
  isOpen,
  onClose,
  onLogin,
  title,
  isLoading = false,
  showLiveUsernameInput = false,
  liveUsername = '',
  onLiveUsernameChange = null,
  startupMode = false, // New prop to indicate if this is just for starting service
  credentialsType = 'default' // Type of credentials (default, instagrapi, etc.)
}) => {
  const [credentials, setCredentials] = useState({
    username: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [saveCredentials, setSaveCredentials] = useState(true);
  const [hasSavedCredentials, setHasSavedCredentials] = useState(false);
  const [savedUsername, setSavedUsername] = useState('');

  // Load saved credentials when popup opens
  useEffect(() => {
    if (isOpen) {
      checkSavedCredentials();
    }
  }, [isOpen, credentialsType]);

  const checkSavedCredentials = () => {
    const saved = credentialsService.getSavedCredentials(credentialsType);
    if (saved) {
      setHasSavedCredentials(true);
      setSavedUsername(saved.username);

      // Auto-fill credentials if not in startup mode
      if (!startupMode) {
        setCredentials({
          username: saved.username,
          password: saved.password
        });
      }
    } else {
      setHasSavedCredentials(false);
      setSavedUsername('');
    }
  };

  const handleLogout = () => {
    credentialsService.clearSavedCredentials(credentialsType);
    setCredentials({ username: '', password: '' });
    setHasSavedCredentials(false);
    setSavedUsername('');
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    // If in startup mode, don't require credentials
    if (startupMode) {
      if (showLiveUsernameInput && !liveUsername.trim()) {
        return;
      }
      onLogin({});
      return;
    }

    // Normal login mode - require credentials (unless using saved credentials)
    if (!hasSavedCredentials && (!credentials.username.trim() || !credentials.password.trim())) {
      return;
    }
    if (showLiveUsernameInput && !liveUsername.trim()) {
      return;
    }

    // Use saved credentials if available, otherwise use form input
    const credentialsToUse = hasSavedCredentials
      ? credentialsService.getSavedCredentials(credentialsType)
      : credentials;

    // Save credentials if user wants to save them (only for new credentials)
    if (!hasSavedCredentials && saveCredentials) {
      credentialsService.saveCredentials(credentials.username, credentials.password, credentialsType);
    }

    onLogin({
      username: credentialsToUse.username,
      password: credentialsToUse.password,
      saveCredentials: hasSavedCredentials ? true : saveCredentials
    });
  };

  const handleClose = () => {
    if (!hasSavedCredentials) {
      setCredentials({ username: '', password: '' });
    }
    setShowPassword(false);
    setSaveCredentials(true);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
            disabled={isLoading}
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* Saved credentials info */}
          {hasSavedCredentials && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-3">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-green-800">Đã lưu thông tin đăng nhập</p>
                  <p className="text-xs text-green-600">@{savedUsername}</p>
                </div>
                <button
                  type="button"
                  onClick={handleLogout}
                  className="flex items-center px-2 py-1 text-xs text-red-600 hover:text-red-800 hover:bg-red-50 rounded transition-colors"
                  disabled={isLoading}
                >
                  <LogOut className="h-3 w-3 mr-1" />
                  Đăng xuất
                </button>
              </div>
            </div>
          )}
          {/* Live Username Input (conditional) */}
          {showLiveUsernameInput && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tên Instagram Live
              </label>
              <input
                type="text"
                value={liveUsername}
                onChange={(e) => onLiveUsernameChange && onLiveUsernameChange(e.target.value)}
                placeholder="Nhập tên Instagram Live..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={isLoading}
                autoFocus={!liveUsername}
              />
              <p className="text-xs text-gray-500 mt-1">
                Ví dụ: nếu URL là instagram.com/username/live thì nhập "username"
              </p>
            </div>
          )}

          {/* Only show credentials input if not in startup mode and no saved credentials */}
          {!startupMode && !hasSavedCredentials && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tên đăng nhập Instagram
                </label>
                <input
                  type="text"
                  value={credentials.username}
                  onChange={(e) => setCredentials(prev => ({ ...prev, username: e.target.value }))}
                  placeholder="Nhập tên đăng nhập"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  disabled={isLoading}
                  autoFocus={!showLiveUsernameInput || !!liveUsername}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Mật khẩu Instagram
                </label>
                <div className="relative">
                  <input
                    type={showPassword ? 'text' : 'password'}
                    value={credentials.password}
                    onChange={(e) => setCredentials(prev => ({ ...prev, password: e.target.value }))}
                    placeholder="Nhập mật khẩu"
                    className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    disabled={isLoading}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    disabled={isLoading}
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                </div>
              </div>

              {/* Save credentials checkbox */}
              <div className="mb-4">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    className="h-4 w-4 text-blue-600"
                    checked={saveCredentials}
                    onChange={(e) => setSaveCredentials(e.target.checked)}
                    disabled={isLoading}
                  />
                  <span className="text-sm">Lưu thông tin đăng nhập để tự động đăng nhập lần sau</span>
                </label>
              </div>
            </>
          )}

          {/* Info */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <p className="text-sm text-blue-700">
              <strong>Lưu ý:</strong> {startupMode
                ? 'Sẽ sử dụng thông tin đăng nhập đã lưu để khởi động dịch vụ.'
                : hasSavedCredentials
                  ? 'Sử dụng thông tin đăng nhập đã lưu hoặc nhập thông tin mới.'
                  : saveCredentials
                    ? 'Thông tin đăng nhập sẽ được lưu để tự động đăng nhập lần sau.'
                    : 'Thông tin đăng nhập chỉ được sử dụng cho phiên này và sẽ không được lưu.'
              }
            </p>
          </div>

          {/* Buttons */}
          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={handleClose}
              className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
              disabled={isLoading}
            >
              Hủy
            </button>
            <button
              type="submit"
              disabled={
                isLoading ||
                (!startupMode && !hasSavedCredentials && (!credentials.username.trim() || !credentials.password.trim())) ||
                (showLiveUsernameInput && !liveUsername.trim())
              }
              className="flex-1 flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
            >
              {isLoading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  {startupMode ? 'Đang khởi động...' : hasSavedCredentials ? 'Đang bắt đầu...' : 'Đang đăng nhập...'}
                </>
              ) : (
                <>
                  <LogIn className="h-4 w-4 mr-2" />
                  {startupMode ? 'Khởi động' : hasSavedCredentials ? 'Bắt đầu' : 'Đăng nhập'}
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default LoginPopup;

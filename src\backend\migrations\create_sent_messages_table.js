/**
 * Migration to create sent_messages table for storing sent message details
 * This allows better tracking of sent messages and enables unsend functionality
 */

module.exports = async (db) => {
    console.log('Running migration: create_sent_messages_table.js');

    // Create sent_messages table
    await db.runQuery(`
    CREATE TABLE IF NOT EXISTS sent_messages (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      queue_message_id TEXT,
      instagram_message_id TEXT,
      thread_id TEXT,
      username TEXT NOT NULL,
      message_content TEXT,
      sent_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      unsent_at DATETIME,
      status TEXT DEFAULT 'sent',
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

    // Create indexes for efficient lookups
    await db.runQuery('CREATE INDEX IF NOT EXISTS idx_sent_messages_username ON sent_messages(username)');
    await db.runQuery('CREATE INDEX IF NOT EXISTS idx_sent_messages_instagram_id ON sent_messages(instagram_message_id)');
    await db.runQuery('CREATE INDEX IF NOT EXISTS idx_sent_messages_queue_id ON sent_messages(queue_message_id)');
    await db.runQuery('CREATE INDEX IF NOT EXISTS idx_sent_messages_status ON sent_messages(status)');
    await db.runQuery('CREATE INDEX IF NOT EXISTS idx_sent_messages_sent_at ON sent_messages(sent_at)');

    console.log('Migration complete: sent_messages table created with indexes');
}; 
@echo off
echo ================================================
echo Fixing Message Queue API Endpoints
echo ================================================
cd /d "%~dp0"

echo Stopping any running Node.js processes on port 3001...
npx kill-port 3001

echo Creating backup of server.js...
copy src\backend\server.js src\backend\server.js.bak

echo Moving API endpoints to correct location...
node -e "const fs = require('fs'); let content = fs.readFileSync('src/backend/server.js', 'utf8'); const startServerCall = 'startServer();'; const apiEndpoints = content.match(/\/\/ Thêm API endpoint để lấy tin nhắn tiếp theo từ hàng đợi[\s\S]*?\/\/ API endpoint để kiểm tra chi tiết tin nhắn trong hàng đợi[\s\S]*?byCustomerType: {}\n    };\n\n/); if (apiEndpoints) { const apiCode = apiEndpoints[0]; content = content.replace(apiCode, ''); const insertPos = content.indexOf('// Start the server'); content = content.slice(0, insertPos) + apiCode + content.slice(insertPos); fs.writeFileSync('src/backend/server.js', content); console.log('API endpoints moved successfully!'); } else { console.log('Could not find API endpoints pattern'); }"

echo Starting Node.js server...
start cmd /k "node src/backend/server.js"

echo ================================================
echo Server restarted with fixed API endpoints
echo ================================================ 
const { ipc<PERSON><PERSON><PERSON> } = require('electron');

class SimpleInstagramApp {
  constructor() {
    this.serverRunning = false;
    this.webRunning = false;
    this.serverStatusInterval = null;

    this.initializeElements();
    this.setupEventListeners();
    this.loadSettings();
    this.checkServerStatus();
    this.setupServerStatusMonitoring();
  }

  initializeElements() {
    // Control elements
    this.serverStartBtn = document.getElementById('serverStartBtn');
    this.serverStopBtn = document.getElementById('serverStopBtn');
    this.openWebBtn = document.getElementById('openWebBtn');

    // Status elements
    this.serverStatus = document.getElementById('serverStatus');
    this.serverStatusText = document.getElementById('serverStatusText');

    // Content elements
    this.systemLogs = document.getElementById('systemLogs');

    // Mobile access elements
    this.mobileAccessCard = document.getElementById('mobileAccessCard');
    this.qrCode = document.getElementById('qrCode');
    this.mobileUrl = document.getElementById('mobileUrl');
    this.copyUrlBtn = document.getElementById('copyUrlBtn');

    // Modal elements
    this.settingsModal = document.getElementById('settingsModal');
  }

  setupEventListeners() {
    // Control buttons
    this.serverStartBtn.addEventListener('click', () => this.startServer());
    this.serverStopBtn.addEventListener('click', () => this.stopServer());
    this.openWebBtn.addEventListener('click', () => this.openWebInterface());
    this.copyUrlBtn.addEventListener('click', () => this.copyMobileUrl());

    // Settings
    document.getElementById('settingsBtn').addEventListener('click', () => {
      console.log('Settings button clicked');
      this.showSettings();
    });
    document.getElementById('closeSettingsBtn').addEventListener('click', () => this.hideSettings());
    document.getElementById('cancelSettingsBtn').addEventListener('click', () => this.hideSettings());
    document.getElementById('saveSettingsBtn').addEventListener('click', () => this.saveSettings());

    // IPC listeners
    ipcRenderer.on('server-started', () => {
      this.updateServerStatus(true);
    });

    ipcRenderer.on('web-started', (_, data) => {
      this.webRunning = true;
      if (data && data.localIP) {
        this.setupMobileAccess(data.localIP);
      }
    });

    ipcRenderer.on('server-stopped', () => {
      this.updateServerStatus(false);
    });

    ipcRenderer.on('web-stopped', () => {
      this.webRunning = false;
      this.mobileAccessCard.style.display = 'none';
    });

    ipcRenderer.on('server-log', (_, data) => {
      this.addLog(data, 'info');
    });

    ipcRenderer.on('server-error', (_, data) => {
      this.addLog(data, 'error');
    });

    ipcRenderer.on('web-log', (_, data) => {
      // Only log important web messages, not all Vite output
      if (data.includes('ready in') || data.includes('Local:') || data.includes('Network:')) {
        this.addLog(`Web: ${data.trim()}`, 'info');
      }
    });

    ipcRenderer.on('web-error', (_, data) => {
      this.addLog(`Web Error: ${data}`, 'error');
    });

    ipcRenderer.on('show-settings', () => {
      this.showSettings();
    });

    // Cleanup on app close
    window.addEventListener('beforeunload', () => {
      if (this.serverStatusInterval) {
        clearInterval(this.serverStatusInterval);
      }
    });
  }

  async loadSettings() {
    try {
      const autoStartServer = await ipcRenderer.invoke('get-store-value', 'autoStartServer', true);
      const autoStartWeb = await ipcRenderer.invoke('get-store-value', 'autoStartWeb', true);
      const enableNotifications = await ipcRenderer.invoke('get-store-value', 'enableNotifications', true);
      const serverPort = await ipcRenderer.invoke('get-store-value', 'serverPort', 3001);

      document.getElementById('autoStartServer').checked = autoStartServer;
      document.getElementById('autoStartWeb').checked = autoStartWeb;
      document.getElementById('enableNotifications').checked = enableNotifications;
      document.getElementById('serverPort').value = serverPort;
    } catch (error) {
      console.error('Failed to load settings:', error);
    }
  }

  async saveSettings() {
    try {
      const autoStartServer = document.getElementById('autoStartServer').checked;
      const autoStartWeb = document.getElementById('autoStartWeb').checked;
      const enableNotifications = document.getElementById('enableNotifications').checked;
      const serverPort = document.getElementById('serverPort').value;

      await ipcRenderer.invoke('set-store-value', 'autoStartServer', autoStartServer);
      await ipcRenderer.invoke('set-store-value', 'autoStartWeb', autoStartWeb);
      await ipcRenderer.invoke('set-store-value', 'enableNotifications', enableNotifications);
      await ipcRenderer.invoke('set-store-value', 'serverPort', parseInt(serverPort));

      this.hideSettings();
      this.addLog('Cài đặt đã được lưu', 'success');
    } catch (error) {
      console.error('Failed to save settings:', error);
      this.addLog('Lỗi khi lưu cài đặt', 'error');
    }
  }

  async openWebInterface() {
    try {
      await ipcRenderer.invoke('open-web-interface');
      this.addLog('Đã mở web interface', 'info');
    } catch (error) {
      console.error('Failed to open web interface:', error);
      this.addLog('Lỗi khi mở web interface', 'error');
    }
  }

  setupServerStatusMonitoring() {
    // Check server status every 10 seconds
    this.serverStatusInterval = setInterval(async () => {
      await this.checkServerStatus();
    }, 10000);
  }

  async checkServerStatus() {
    try {
      const response = await fetch('http://localhost:3001/api/health');
      if (response.ok) {
        this.updateServerStatus(true);
      } else {
        this.updateServerStatus(false);
      }
    } catch (error) {
      this.updateServerStatus(false);
    }
  }

  updateServerStatus(running) {
    const wasRunning = this.serverRunning;
    this.serverRunning = running;

    if (running) {
      this.serverStatus.className = 'status-indicator status-online';
      this.serverStatusText.textContent = 'Server đang chạy';
      this.serverStartBtn.disabled = true;
      this.serverStopBtn.disabled = false;

      if (!wasRunning) {
        this.addLog('Server đã khởi động', 'success');
      }
    } else {
      this.serverStatus.className = 'status-indicator status-offline';
      this.serverStatusText.textContent = 'Server đã dừng';
      this.serverStartBtn.disabled = false;
      this.serverStopBtn.disabled = true;

      if (wasRunning) {
        this.addLog('Server đã dừng', 'warning');
        // Hide mobile access card when server stops
        this.mobileAccessCard.style.display = 'none';
      }
    }
  }

  async startServer() {
    try {
      this.addLog('Đang khởi động server...', 'info');
      await ipcRenderer.invoke('start-server');
    } catch (error) {
      console.error('Failed to start server:', error);
      this.addLog('Lỗi khi khởi động server', 'error');
    }
  }

  async stopServer() {
    try {
      this.addLog('Đang dừng server...', 'info');
      await ipcRenderer.invoke('stop-server');
    } catch (error) {
      console.error('Failed to stop server:', error);
      this.addLog('Lỗi khi dừng server', 'error');
    }
  }

  addLog(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry log-${type}`;
    logEntry.textContent = `[${timestamp}] ${message}`;

    this.systemLogs.appendChild(logEntry);
    this.systemLogs.scrollTop = this.systemLogs.scrollHeight;

    // Keep only last 100 log entries
    while (this.systemLogs.children.length > 100) {
      this.systemLogs.removeChild(this.systemLogs.firstChild);
    }
  }

  showSettings() {
    console.log('showSettings called');
    console.log('settingsModal:', this.settingsModal);
    this.settingsModal.classList.remove('hidden');
  }

  hideSettings() {
    this.settingsModal.classList.add('hidden');
  }

  async setupMobileAccess(localIP) {
    try {
      // Use production web for mobile access
      const mobileUrl = `http://${localIP}:3001/web`;

      // Update mobile URL display
      this.mobileUrl.textContent = mobileUrl;

      // Generate QR code
      const qrCodeDataURL = await ipcRenderer.invoke('generate-qr-code', mobileUrl);
      if (qrCodeDataURL) {
        this.qrCode.src = qrCodeDataURL;
        this.qrCode.style.display = 'block';
      }

      // Show mobile access card
      this.mobileAccessCard.style.display = 'block';

      this.addLog(`📱 Mobile access: ${mobileUrl}`, 'success');
    } catch (error) {
      console.error('Failed to setup mobile access:', error);
      this.addLog('Lỗi khi thiết lập truy cập mobile', 'error');
    }
  }

  copyMobileUrl() {
    const url = this.mobileUrl.textContent;
    if (url && url !== 'Đang tải...') {
      navigator.clipboard.writeText(url).then(() => {
        this.addLog('Đã sao chép URL vào clipboard', 'success');

        // Visual feedback
        const originalText = this.copyUrlBtn.textContent;
        this.copyUrlBtn.textContent = '✅ Đã sao chép';
        this.copyUrlBtn.classList.add('bg-green-500');
        this.copyUrlBtn.classList.remove('bg-blue-500');

        setTimeout(() => {
          this.copyUrlBtn.textContent = originalText;
          this.copyUrlBtn.classList.remove('bg-green-500');
          this.copyUrlBtn.classList.add('bg-blue-500');
        }, 2000);
      }).catch(() => {
        this.addLog('Lỗi khi sao chép URL', 'error');
      });
    }
  }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new SimpleInstagramApp();
});

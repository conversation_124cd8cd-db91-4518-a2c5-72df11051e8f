Stack trace:
Frame         Function      Args
0007FFFF8E90  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF7D90) msys-2.0.dll+0x1FEBA
0007FFFF8E90  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9168) msys-2.0.dll+0x67F9
0007FFFF8E90  000210046832 (000210285FF9, 0007FFFF8D48, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF8E90  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF8E90  0002100690B4 (0007FFFF8EA0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFF9170  00021006A49D (0007FFFF8EA0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFBD13E0000 ntdll.dll
7FFBD0300000 KERNEL32.DLL
7FFBCE580000 KERNELBASE.dll
7FFBCA870000 apphelp.dll
7FFBD1150000 USER32.dll
7FFBCEF90000 win32u.dll
7FFBCFD40000 GDI32.dll
7FFBCF080000 gdi32full.dll
7FFBCEE50000 msvcp_win.dll
7FFBCED00000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFBCF6D0000 advapi32.dll
7FFBCF590000 msvcrt.dll
7FFBD0E80000 sechost.dll
7FFBD0F80000 RPCRT4.dll
7FFBCDBA0000 CRYPTBASE.DLL
7FFBCEC60000 bcryptPrimitives.dll
7FFBD0E40000 IMM32.DLL

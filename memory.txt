
# Technology Stack & Architecture
- Electron + React for desktop app (CommiLive) with Node.js + Express + Puppeteer for backend automation and Socket.IO for real-time communication
- Standalone desktop application with auto-startup of server and web components
- Web interface opens on port 3000 with QR code for mobile access using IPv4 addresses (192.168.xxx.xxx format)
- MongoDB Atlas sync: load and merge on startup, then auto-sync local changes in real-time for multi-device usage
- Real-time auto-sync applies changes immediately to both local database and MongoDB Atlas after successful sync
- MongoDB sync optimization uses content hash comparison with boolean normalization, but has a bug where previously synced records get updated again on every startup
- MongoDB Atlas sync has a bug where bulk deletion of printed comments doesn't properly update all records to is_deleted=true
- User wants to restructure all database tables from API comment detection through printing to history storage, optimized for API-based comment flow rather than DOM scraping approach, with API comments stored only in session memory without dedicated database tables.

# UI/UX Design
- Vietnamese interface with minimalist design and Instagram-like comment display (newest at bottom)
- Comment layout: star icon (left), username/timestamp (top) with comment text (below), backup and print buttons (right)
- Auto-scroll to newest comment after 5-8s of no user interaction, disabled during active scrolling
- Customizable comment colors for new vs regular customers with adjustable preferences
- Responsive design supporting minimum iPhone 12 Pro screen size with mobile-specific layout adjustments
- UI shows login status ('chưa đăng nhập'/'chưa khởi động') and direct startup interface when cookies exist
- Sync status indicators with icons displayed on printed history and customer management pages
- Status indicators (đã in, đã gửi, chờ) on status bar only show changes within current session and reset on restart

# Instagram Scraper & Auto-Messaging
- Cookie-based persistent login system with separate storage for scraper and auto-messaging accounts
- Instagram thread IDs enable direct navigation to cached threads via https://www.instagram.com/direct/t/{thread_id}
- Auto-messaging uses ~2 second delays between steps with 30ms typing delay and Alt+Enter support for line breaks
- Simple FIFO message queue processing without priority - send messages in order they were queued
- Failed auto-messages moved to failed_message table with delete and retry buttons for queue management
- Auto-messaging service automatically restarts when it fails or when CPU usage reaches 100%
- Chrome kill/restart functionality only applies to auto-messaging service, not scraper service
- API interception for Instagram Live comments implemented instead of DOM scraping, using unique pk IDs from Instagram's API
- Instagram Live comment API has an initial get_comment call with 'is_first_fetch': 'True' parameter before regular get_comment calls, which is essential for capturing the first comment.
- User prefers to optimize Instagram Live access verification by removing unnecessary delays - once /live page loads and click-to-play is successful, immediately start API monitoring without additional verification steps since reaching that point guarantees comment collection will work.
- When closing the app, ALL Chrome testing processes should be killed, including hidden/background processes

# Printing System
- HPRT TP80N thermal printer (80mm) requiring Vietnamese text as bitmap/image due to diacritic issues
- Two print types: single comment printing and user history printing
- Customizable thermal bill format with settings for font size, style, alignment, bold/italic and preview
- Backup buttons send backup-type templates and print comments with '(dự bị)' text after username
- Option to print to text file for testing when no physical printer is available
- When re-printing comments, system should include a separate resend message button in the history interface
- Duplicate printed history records are intentional - users can print same comment multiple times and each print should be saved as separate record with unique ID per print instance.

# Templates & Comment Processing
- System automatically sends pre-configured template messages when print button is pressed
- Templates include dynamic content (username, comment text) and are sent as separate messages
- Send_once templates only sent once per user (applies to both normal and backup templates)
- Send_once template records automatically deleted after 2 days
- Price extraction handles patterns with prefixes/suffixes (t100→100, 190s→190, 50k→50, @180→180)
- Customizable price conversion in templates where prefix characters map to prices with configurable settings

# History & Management
- History page with individual/bulk deletion, date/time filtering, and user-based organization
- Print history organized by user with expandable sections showing comments with timestamps
- Delete operations only affect currently displayed/filtered items
- Default display mode shows all items expanded rather than collapsed
- Thread IDs, printed_history, and send_once_history synced to MongoDB Atlas with intelligent sync
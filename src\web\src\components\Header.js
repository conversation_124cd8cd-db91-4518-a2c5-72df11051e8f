import React from 'react';
import { Menu, X, Wifi, WifiOff } from 'lucide-react';
import { useApp } from '../contexts/AppContext';
import { useSocket } from '../contexts/SocketContext';

const Header = () => {
  const { state, actions } = useApp();
  const { isConnected, systemState } = useSocket();
  const { sidebarOpen } = state;

  const toggleSidebar = () => {
    actions.setSidebarOpen(!sidebarOpen);
  };

  return (
    <header className="sticky top-0 z-50 bg-white shadow-sm border-b border-gray-200 safe-area-top">
      <div className="px-2 sm:px-4 lg:px-8">
        <div className="flex items-center justify-between h-14 sm:h-16">
          {/* Left side */}
          <div className="flex items-center">
            {/* Mobile menu button */}
            <button
              onClick={toggleSidebar}
              className="md:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-sky-500"
            >
              {sidebarOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </button>

            {/* Desktop sidebar toggle */}
            <button
              onClick={toggleSidebar}
              className="hidden md:block p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-sky-500"
            >
              <Menu className="h-5 w-5" />
            </button>

            {/* Logo and title */}
            <div className="flex items-center ml-1">
              <div className="flex-shrink-0">
                <h1 className="text-lg font-bold bg-gradient-to-r from-sky-500 to-teal-500 bg-clip-text text-transparent">
                  CommiLive
                </h1>
              </div>
            </div>
          </div>

          {/* Right side */}
          <div className="flex items-center space-x-3 sm:space-x-6">
            {/* Connection status */}
            <div className="flex items-center space-x-2">
              {isConnected ? (
                <>
                  <Wifi className="h-4 w-4 text-green-500" />
                  <span className="hidden sm:inline text-sm text-green-600 font-medium">
                    Đã kết nối
                  </span>
                </>
              ) : (
                <>
                  <WifiOff className="h-4 w-4 text-red-500" />
                  <span className="hidden sm:inline text-sm text-red-600 font-medium">
                    Mất kết nối
                  </span>
                </>
              )}
            </div>

            {/* Comments count for mobile/tablet - Always show labels */}
            <div className="lg:hidden flex items-center space-x-1 text-xs text-gray-600 overflow-hidden whitespace-nowrap">
              <div className="flex items-center flex-shrink-0">
                <span className="font-medium text-xs">{systemState.totalComments}</span>
                <span className="ml-0.5 text-xs">cmt</span>
              </div>
              <span className="text-gray-400 text-xs">|</span>

              {/* Printed comments count */}
              <div className="flex items-center text-blue-600 flex-shrink-0">
                <span className="font-medium text-xs">{systemState.printedComments}</span>
                <span className="ml-0.5 text-xs">in</span>
              </div>
              <span className="text-gray-400 text-xs">|</span>

              {/* Sent messages count */}
              <div className="flex items-center text-green-600 flex-shrink-0">
                <span className="font-medium text-xs">{systemState.sentMessages}</span>
                <span className="ml-0.5 text-xs">gửi</span>
              </div>
              <span className="text-gray-400 text-xs">|</span>

              {/* Queued messages count */}
              <div className="flex items-center text-orange-600 flex-shrink-0">
                <span className="font-medium text-xs">{systemState.queuedMessages}</span>
                <span className="ml-0.5 text-xs">chờ</span>
              </div>
            </div>

            {/* System status indicator */}
            {systemState.isRunning && (
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="hidden sm:inline text-sm text-gray-600">
                  Đang hoạt động
                </span>
              </div>
            )}

            {/* Quick stats */}
            <div className="hidden lg:flex items-center space-x-4 text-sm text-gray-600">
              <div className="flex items-center space-x-1">
                <span className="font-medium">{systemState.totalComments}</span>
                <span>bình luận</span>
              </div>
              <div className="flex items-center space-x-1 text-blue-600">
                <span className="font-medium">{systemState.printedComments}</span>
                <span>đã in</span>
              </div>
              <div className="flex items-center space-x-1 text-green-600">
                <span className="font-medium">{systemState.sentMessages}</span>
                <span>đã gửi</span>
              </div>
              <div className="flex items-center space-x-1">
                <span className="font-medium">{systemState.queuedMessages}</span>
                <span>chờ gửi</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;

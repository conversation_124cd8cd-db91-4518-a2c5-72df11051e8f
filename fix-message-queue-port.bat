@echo off
echo ================================================
echo Fixing Message Queue Port Configuration
echo ================================================
cd /d "%~dp0"

echo Stopping any running Node.js processes on port 3001...
npx kill-port 3001

echo Setting NODE_SERVER_PORT environment variable...
set NODE_SERVER_PORT=3001

echo Starting Node.js server with fixed configuration...
start cmd /k "node src/backend/server.js"

echo Waiting for Node.js server to initialize (5 seconds)...
timeout /t 5 /nobreak > nul

echo Testing API endpoint...
curl http://localhost:3001/api/health

echo.
echo ================================================
echo Node.js server restarted with port 3001
echo Now restart the Python service using start-instagrapi-service-debug.bat
echo ================================================ 
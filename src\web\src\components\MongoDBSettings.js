import React, { useState, useEffect } from 'react';
import { Database, TestTube, Save, RefreshCw, CheckCircle, XCircle, Cloud, Download } from 'lucide-react';
import toast from 'react-hot-toast';
import api from '../config/api';

const MongoDBSettings = () => {
  const [connectionString, setConnectionString] = useState('');
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  const [status, setStatus] = useState(null);
  const [lastSync, setLastSync] = useState(null);
  const [isChangingConnection, setIsChangingConnection] = useState(false);
  const [isInitialLoading, setIsInitialLoading] = useState(true);

  // Check if server is ready
  const checkServerReady = async () => {
    try {
      const response = await api.get('/api/health');
      return response.status === 200;
    } catch (error) {
      return false;
    }
  };

  useEffect(() => {
    const loadData = async () => {
      // Wait for server to be ready
      let serverReady = false;
      let attempts = 0;
      const maxAttempts = 10;

      while (!serverReady && attempts < maxAttempts) {
        serverReady = await checkServerReady();
        if (!serverReady) {
          console.log(`Server not ready, attempt ${attempts + 1}/${maxAttempts}`);
          await new Promise(resolve => setTimeout(resolve, 2000));
          attempts++;
        }
      }

      if (!serverReady) {
        console.error('Server not ready after maximum attempts');
        setIsInitialLoading(false);
        return;
      }

      try {
        await loadMongoDBSettings();
        await loadMongoDBStatus();
        setIsInitialLoading(false);
      } catch (error) {
        console.error('Failed to load initial data:', error);
        setIsInitialLoading(false);
      }
    };

    loadData();

    // Auto-refresh status every 20 seconds (reduced frequency to avoid spam)
    const interval = setInterval(async () => {
      try {
        await loadMongoDBStatus();
      } catch (error) {
        // Silently fail for auto-refresh
      }
    }, 20000);

    return () => clearInterval(interval);
  }, []);

  const loadMongoDBSettings = async () => {
    try {
      const response = await api.get('/api/mongodb/settings');
      const data = response.data;

      if (data.success) {
        // Set connection string if we have one
        if (data.hasConnectionString) {
          setConnectionString(data.connectionString || '***masked***');
        } else {
          setConnectionString('');
        }

        // Debug log
        console.log('MongoDB Settings loaded:', data);
      }
    } catch (error) {
      if (error.response?.status === 404) {
        console.log('MongoDB settings endpoint not found - server may not be ready');
        return;
      }
      console.error('Failed to load MongoDB settings:', error);
      // Don't show toast for initial load errors
    }
  };

  const loadMongoDBStatus = async () => {
    try {
      const response = await api.get('/api/mongodb/status');
      const data = response.data;

      if (data.success) {
        setStatus(data.status);
        setIsConnected(data.status.isConnected);
        setLastSync(data.lastSync);

        // Debug log
        console.log('MongoDB Status:', data.status);
      }
    } catch (error) {
      if (error.response?.status === 404) {
        console.log('MongoDB status endpoint not found - server may not be ready');
        setIsConnected(false);
        return;
      }
      console.error('Failed to load MongoDB status:', error);
      setIsConnected(false);
    }
  };

  const handleTestConnection = async () => {
    if (!connectionString.trim()) {
      toast.error('Vui lòng nhập connection string');
      return;
    }

    setIsTesting(true);
    try {
      const response = await api.post('/api/mongodb/test', {
        connectionString: connectionString.trim()
      });

      const data = response.data;

      if (data.success) {
        toast.success('Kết nối thành công!');
      } else {
        toast.error(`Lỗi kết nối: ${data.error}`);
      }
    } catch (error) {
      toast.error('Lỗi khi test kết nối');
      console.error('Test connection error:', error);
    } finally {
      setIsTesting(false);
    }
  };

  const handleSaveSettings = async () => {
    if (!connectionString.trim()) {
      toast.error('Vui lòng nhập connection string');
      return;
    }

    setIsLoading(true);
    try {
      const response = await api.post('/api/mongodb/settings', {
        connectionString: connectionString.trim()
      });

      const data = response.data;

      if (data.success) {
        toast.success('Kết nối MongoDB Atlas thành công! Database "instagram-live" đã được tạo.');
        setIsConnected(true);
        setIsChangingConnection(false);
        await loadMongoDBStatus();
      } else {
        toast.error(`Lỗi kết nối: ${data.error}`);
      }
    } catch (error) {
      toast.error('Lỗi khi lưu cài đặt');
      console.error('Save settings error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDisconnect = async () => {
    setIsLoading(true);
    try {
      const response = await api.post('/api/mongodb/disconnect');
      const data = response.data;

      if (data.success) {
        toast.success('Đã ngắt kết nối MongoDB');
        setIsConnected(false);
        await loadMongoDBStatus();
      } else {
        toast.error(`Lỗi: ${data.error}`);
      }
    } catch (error) {
      toast.error('Lỗi khi ngắt kết nối');
      console.error('Disconnect error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSyncToMongo = async () => {
    if (!isConnected) {
      toast.error('Chưa kết nối với MongoDB');
      return;
    }

    setIsSyncing(true);
    try {
      const response = await api.post('/api/mongodb/sync-to-mongo');
      const data = response.data;

      if (data.success) {
        toast.success(`Đã đồng bộ ${data.count} khách hàng lên MongoDB`);
        setLastSync(new Date().toISOString());
      } else {
        toast.error(`Lỗi đồng bộ: ${data.error}`);
      }
    } catch (error) {
      toast.error('Lỗi khi đồng bộ lên MongoDB');
      console.error('Sync to mongo error:', error);
    } finally {
      setIsSyncing(false);
    }
  };

  const handleSyncFromMongo = async () => {
    if (!isConnected) {
      toast.error('Chưa kết nối với MongoDB');
      return;
    }

    setIsSyncing(true);
    try {
      const response = await api.post('/api/mongodb/smart-sync');
      const data = response.data;

      if (data.success) {
        const message = data.message || `Đồng bộ thông minh hoàn tất: +${data.addedToLocal || 0} local, +${data.addedToMongo || 0} MongoDB`;
        toast.success(message);
        setLastSync(new Date().toISOString());
      } else {
        toast.error(`Lỗi đồng bộ: ${data.error}`);
      }
    } catch (error) {
      toast.error('Lỗi khi đồng bộ từ MongoDB');
      console.error('Smart sync error:', error);
    } finally {
      setIsSyncing(false);
    }
  };

  if (isInitialLoading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-center py-8">
          <div className="spinner mr-3" />
          <span className="text-gray-600">Đang tải cài đặt MongoDB...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <Database className="h-6 w-6 text-green-600" />
          <div>
            <h3 className="text-lg font-semibold text-gray-900">MongoDB Atlas</h3>
            <p className="text-sm text-gray-500">Đồng bộ dữ liệu khách hàng với cloud database</p>
          </div>
        </div>

        {/* Connection Status */}
        <div className="flex items-center space-x-2">
          {isConnected ? (
            <div className="flex items-center space-x-2 text-green-600">
              <CheckCircle className="h-5 w-5" />
              <span className="text-sm font-medium">Đã kết nối</span>
            </div>
          ) : (
            <div className="flex items-center space-x-2 text-gray-500">
              <XCircle className="h-5 w-5" />
              <span className="text-sm font-medium">Chưa kết nối</span>
            </div>
          )}
        </div>
      </div>

      {/* Connection String Input */}
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            MongoDB Atlas Connection String
          </label>
          <input
            type="password"
            value={isConnected && !isChangingConnection && connectionString === '***masked***' ? '' : connectionString}
            onChange={(e) => {
              setConnectionString(e.target.value);
              if (isConnected && !isChangingConnection) {
                setIsChangingConnection(true);
              }
            }}
            placeholder={isConnected && !isChangingConnection ? "✅ Đã kết nối MongoDB Atlas" : "mongodb+srv://username:<EMAIL>/database"}
            className={`input w-full ${isConnected && !isChangingConnection ? 'bg-green-50 border-green-300' : ''}`}
            disabled={isLoading || (isConnected && !isChangingConnection && connectionString === '***masked***')}
          />
          <p className="text-xs text-gray-500 mt-1">
            Nhập connection string từ MongoDB Atlas. Database "instagram-live" sẽ được tạo tự động nếu chưa có.
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-3">
          <button
            onClick={handleTestConnection}
            disabled={isTesting || isLoading || !connectionString.trim()}
            className="btn-secondary flex items-center space-x-2"
          >
            {isTesting ? (
              <div className="spinner" />
            ) : (
              <TestTube className="h-4 w-4" />
            )}
            <span>{isTesting ? 'Đang test...' : 'Test kết nối'}</span>
          </button>

          {!isConnected || isChangingConnection ? (
            <button
              onClick={handleSaveSettings}
              disabled={isLoading || !connectionString.trim() || connectionString === '***masked***'}
              className="btn-primary flex items-center space-x-2"
            >
              {isLoading ? (
                <div className="spinner" />
              ) : (
                <Save className="h-4 w-4" />
              )}
              <span>{isLoading ? 'Đang kết nối...' : 'Lưu & Kết nối'}</span>
            </button>
          ) : (
            <div className="flex space-x-3">
              <button
                onClick={() => {
                  setConnectionString('');
                  setIsChangingConnection(true);
                }}
                className="btn-secondary flex items-center space-x-2"
              >
                <RefreshCw className="h-4 w-4" />
                <span>Đổi kết nối</span>
              </button>
              <button
                onClick={handleDisconnect}
                disabled={isLoading}
                className="btn-danger flex items-center space-x-2"
              >
                {isLoading ? (
                  <div className="spinner" />
                ) : (
                  <XCircle className="h-4 w-4" />
                )}
                <span>{isLoading ? 'Đang ngắt...' : 'Ngắt kết nối'}</span>
              </button>
            </div>
          )}
        </div>

        {/* Sync Controls */}
        {isConnected && (
          <div className="border-t pt-4 mt-6">
            <h4 className="text-md font-medium text-gray-900 mb-3">Đồng bộ dữ liệu thông minh</h4>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
              <div className="flex items-start space-x-2">
                <div className="text-blue-600 mt-0.5">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="text-sm text-blue-800">
                  <p className="font-medium mb-1">Tính năng đồng bộ thông minh:</p>
                  <ul className="text-xs space-y-1">
                    <li>• Tự động đồng bộ khi khởi động app</li>
                    <li>• Đồng bộ hai chiều: thêm thiếu, cập nhật mới nhất</li>
                    <li>• Tự động sync mỗi 5 phút khi có kết nối</li>
                    <li>• Thay đổi local tự động sync lên Atlas ngay lập tức</li>
                  </ul>
                </div>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <button
                onClick={handleSyncToMongo}
                disabled={isSyncing}
                className="btn-primary flex items-center justify-center space-x-2"
              >
                {isSyncing ? (
                  <div className="spinner" />
                ) : (
                  <Cloud className="h-4 w-4" />
                )}
                <span>Đồng bộ lên MongoDB</span>
              </button>

              <button
                onClick={handleSyncFromMongo}
                disabled={isSyncing}
                className="btn-secondary flex items-center justify-center space-x-2"
              >
                {isSyncing ? (
                  <div className="spinner" />
                ) : (
                  <Download className="h-4 w-4" />
                )}
                <span>Đồng bộ thông minh</span>
              </button>
            </div>

            {lastSync && (
              <p className="text-xs text-gray-500 mt-2">
                Đồng bộ lần cuối: {new Date(lastSync).toLocaleString('vi-VN')}
              </p>
            )}
          </div>
        )}

        {/* Status Info */}
        {status && isConnected && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mt-4">
            <h4 className="text-sm font-medium text-green-900 mb-2">Thông tin kết nối</h4>
            <div className="text-sm text-green-700 space-y-1">
              <p><strong>Database:</strong> {status.database}</p>
              <p><strong>Connection:</strong> {status.connectionString}</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MongoDBSettings;

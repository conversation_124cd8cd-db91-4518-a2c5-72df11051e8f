import React, { useState, useEffect } from 'react';
import { Play, Square, RefreshCw, AlertCircle, CheckCircle, Bug, TestTube, LogOut, LogIn } from 'lucide-react';
import { useSocket } from '../contexts/SocketContext';
import toast from 'react-hot-toast';
import api from '../config/api';
import LoginPopup from './LoginPopup';

const ScrapingControl = () => {
  const { systemState, isConnected } = useSocket();
  const [isStarting, setIsStarting] = useState(false);
  const [isStopping, setIsStopping] = useState(false);
  const [isDebugging, setIsDebugging] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const [savedCookies, setSavedCookies] = useState(null);
  const [credentials, setCredentials] = useState({
    username: '',
    password: ''
  });
  const [liveUsername, setLiveUsername] = useState('');
  const [showLoginPopup, setShowLoginPopup] = useState(false);
  const [scrapingMode, setScrapingMode] = useState(null);

  // Load saved cookies and scraping mode on component mount
  useEffect(() => {
    loadSavedCookies();
    loadScrapingMode();
  }, []);

  const loadScrapingMode = async () => {
    try {
      const response = await api.get('/api/scraping-mode');
      if (response.data.success) {
        setScrapingMode(response.data.mode);
      }
    } catch (error) {
      console.error('Failed to load scraping mode:', error);
    }
  };

  const loadSavedCookies = async () => {
    try {
      const response = await api.get('/api/saved-cookies');
      if (response.data.success && response.data.hasCookies) {
        setSavedCookies({
          username: response.data.username
        });
      }
    } catch (error) {
      console.error('Failed to load saved cookies:', error);
    }
  };

  // Toggle functionality removed - API only mode

  const handleLogout = async () => {
    setIsLoggingOut(true);
    try {
      const response = await api.post('/api/logout');
      if (response.data.success) {
        setSavedCookies(null);
        setCredentials({ username: '', password: '' });
        toast.success('Đã đăng xuất thành công - Cookies đã được xóa');
      }
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Lỗi khi đăng xuất';
      toast.error(errorMessage);
      console.error('Logout error:', error);
    } finally {
      setIsLoggingOut(false);
    }
  };

  const handleStart = async () => {
    if (!liveUsername.trim()) {
      toast.error('Vui lòng nhập tên người dùng Instagram');
      return;
    }

    // Use saved cookies if available, otherwise use form input
    let credentialsToUse = null;
    let useSavedCookies = false;

    if (savedCookies) {
      useSavedCookies = true;
      toast('Đang sử dụng phiên đăng nhập đã lưu...', { icon: 'ℹ️' });
    } else {
      if (!credentials.username.trim() || !credentials.password.trim()) {
        toast.error('Vui lòng nhập thông tin đăng nhập Instagram');
        return;
      }
      credentialsToUse = {
        username: credentials.username.trim(),
        password: credentials.password.trim()
      };
    }

    setIsStarting(true);
    try {
      const response = await api.post('/api/start-scraping', {
        liveUsername: liveUsername.trim(),
        credentials: credentialsToUse,
        useSavedCookies
      });

      if (response.data.success) {
        if (useSavedCookies) {
          toast.success('Đã bắt đầu theo dõi Instagram Live bằng phiên đăng nhập đã lưu');
        } else {
          toast.success('Đã bắt đầu theo dõi Instagram Live - Phiên đăng nhập đã được lưu');
          // Reload saved cookies in case they were just saved
          await loadSavedCookies();
        }
        // Reload scraping mode to get updated stats
        await loadScrapingMode();
      }
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Lỗi khi bắt đầu theo dõi';
      toast.error(errorMessage);
      console.error('Start scraping error:', error);
    } finally {
      setIsStarting(false);
    }
  };

  const handleStop = async () => {
    setIsStopping(true);
    try {
      const response = await api.post('/api/stop-scraping');

      if (response.data.success) {
        toast.success('Đã dừng theo dõi Instagram Live');
        // Reload scraping mode to reset stats
        await loadScrapingMode();
      }
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Lỗi khi dừng theo dõi';
      toast.error(errorMessage);
      console.error('Stop scraping error:', error);
    } finally {
      setIsStopping(false);
    }
  };

  const handleDebug = async () => {
    if (!systemState.isRunning) {
      toast.error('Vui lòng bắt đầu theo dõi trước khi debug');
      return;
    }

    setIsDebugging(true);
    try {
      const response = await api.post('/api/debug-comments');

      if (response.data.success) {
        toast.success('Debug hoàn thành - kiểm tra console logs của server');
      }
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Lỗi khi debug';
      toast.error(errorMessage);
      console.error('Debug error:', error);
    } finally {
      setIsDebugging(false);
    }
  };

  const handleTestComment = async () => {
    setIsTesting(true);
    try {
      const response = await api.post('/api/test-comment');

      if (response.data.success) {
        toast.success('Test comment đã được gửi - kiểm tra Comments page');
        console.log('Test comment sent:', response.data.comment);
      }
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Lỗi khi gửi test comment';
      toast.error(errorMessage);
      console.error('Test comment error:', error);
    } finally {
      setIsTesting(false);
    }
  };

  const handlePopupLogin = async (loginCredentials) => {
    if (!liveUsername.trim()) {
      toast.error('Vui lòng nhập tên người dùng Instagram Live trước');
      return;
    }

    setIsStarting(true);
    try {
      const response = await api.post('/api/start-scraping', {
        liveUsername: liveUsername.trim(),
        credentials: {
          username: loginCredentials.username.trim(),
          password: loginCredentials.password.trim()
        },
        useSavedCookies: false,
        saveCredentials: loginCredentials.saveCredentials || false
      });

      if (response.data.success) {
        const message = loginCredentials.saveCredentials
          ? 'Đã bắt đầu thu thập bình luận - Phiên đăng nhập đã được lưu'
          : 'Đã bắt đầu thu thập bình luận';
        toast.success(message);
        setShowLoginPopup(false);
        // Reload saved cookies
        await loadSavedCookies();
      }
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Lỗi khi bắt đầu thu thập';
      toast.error(errorMessage);
      console.error('Start scraping error:', error);
    } finally {
      setIsStarting(false);
    }
  };

  const getStatusColor = () => {
    if (!isConnected) return 'text-gray-500';
    if (systemState.isRunning) return 'text-green-500';
    return 'text-red-500';
  };

  const getStatusText = () => {
    if (!isConnected) return 'Mất kết nối';
    if (systemState.isRunning) return 'Đang hoạt động';
    return 'Đã dừng';
  };

  const getStatusIcon = () => {
    if (!isConnected) return <AlertCircle className="h-5 w-5" />;
    if (systemState.isRunning) return <CheckCircle className="h-5 w-5" />;
    return <AlertCircle className="h-5 w-5" />;
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Thu thập bình luận</h3>
          <p className="text-sm text-gray-500">Quản lý việc theo dõi bình luận Instagram Live</p>
        </div>
        <div className={`flex items-center space-x-2 ${getStatusColor()}`}>
          {getStatusIcon()}
          <span className="font-medium">{getStatusText()}</span>
        </div>
      </div>

      <div className="space-y-4">
        {/* Live Username Input */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Tên người dùng Instagram Live
          </label>
          <input
            type="text"
            value={liveUsername}
            onChange={(e) => setLiveUsername(e.target.value)}
            placeholder="Nhập tên người dùng (không cần @)"
            className="input"
            disabled={systemState.isRunning}
          />
          <p className="text-xs text-gray-500 mt-1">
            Ví dụ: nếu URL là instagram.com/username/live thì nhập "username"
          </p>
        </div>

        {/* Credentials */}
        {savedCookies ? (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
              <div className="flex-1">
                <h4 className="text-sm font-medium text-green-800">Phiên đăng nhập đã lưu</h4>
                <p className="text-sm text-green-700">
                  Tài khoản: <span className="font-medium">@{savedCookies.username}</span>
                </p>
                <p className="text-xs text-green-600 mt-1 hidden sm:block">
                  Bạn có thể bắt đầu thu thập mà không cần đăng nhập lại. Cookies sẽ tự động được sử dụng.
                </p>
                <p className="text-xs text-green-600 mt-1 sm:hidden">
                  Sẵn sàng thu thập với tài khoản đã lưu
                </p>
              </div>
              <button
                onClick={handleLogout}
                disabled={isLoggingOut || systemState.isRunning}
                className="btn-secondary flex items-center justify-center text-sm w-full sm:w-auto"
              >
                {isLoggingOut ? (
                  <div className="spinner mr-2" />
                ) : (
                  <LogOut className="h-4 w-4 mr-2" />
                )}
                {isLoggingOut ? 'Đang xóa cookies...' : 'Xóa cookies'}
              </button>
            </div>
          </div>
        ) : (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
              <div className="flex-1">
                <h4 className="text-sm font-medium text-red-800">Cần đăng nhập</h4>
                <p className="text-sm text-red-700">
                  Vui lòng đăng nhập Instagram để bắt đầu thu thập bình luận
                </p>
              </div>
              <button
                onClick={() => setShowLoginPopup(true)}
                disabled={systemState.isRunning}
                className="btn-primary flex items-center justify-center text-sm w-full sm:w-auto"
              >
                <LogIn className="h-4 w-4 mr-2" />
                Đăng nhập
              </button>
            </div>
          </div>
        )}

        {/* Control Buttons */}
        <div className="flex space-x-3 pt-4">
          {!systemState.isRunning ? (
            <button
              onClick={handleStart}
              disabled={isStarting || !isConnected}
              className="btn-primary flex items-center"
            >
              {isStarting ? (
                <div className="spinner mr-2" />
              ) : (
                <Play className="h-4 w-4 mr-2" />
              )}
              {isStarting ? 'Đang bắt đầu...' : 'Bắt đầu theo dõi'}
            </button>
          ) : (
            <button
              onClick={handleStop}
              disabled={isStopping}
              className="btn-danger flex items-center"
            >
              {isStopping ? (
                <div className="spinner mr-2" />
              ) : (
                <Square className="h-4 w-4 mr-2" />
              )}
              {isStopping ? 'Đang dừng...' : 'Dừng theo dõi'}
            </button>
          )}

          <button
            onClick={() => window.location.reload()}
            className="btn-secondary flex items-center"
            disabled={isStarting || isStopping}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Làm mới
          </button>

          {systemState.isRunning && (
            <button
              onClick={handleDebug}
              disabled={isDebugging}
              className="btn-warning flex items-center"
            >
              {isDebugging ? (
                <div className="spinner mr-2" />
              ) : (
                <Bug className="h-4 w-4 mr-2" />
              )}
              {isDebugging ? 'Đang debug...' : 'Debug Comments'}
            </button>
          )}

          <button
            onClick={handleTestComment}
            disabled={isTesting}
            className="btn-secondary flex items-center"
          >
            {isTesting ? (
              <div className="spinner mr-2" />
            ) : (
              <TestTube className="h-4 w-4 mr-2" />
            )}
            {isTesting ? 'Đang test...' : 'Test Comment'}
          </button>
        </div>

        {/* Scraping Mode - API Only */}
        {scrapingMode && (
          <div className="bg-green-50 rounded-lg p-4 mt-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium text-green-900 mb-1">Chế độ Scraping</h4>
                <div className="flex items-center space-x-2">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    🚀 Instagram API Interception
                  </span>
                  <span className="text-xs text-green-600 font-medium">
                    ✓ Không giới hạn comments | ✓ Unique IDs | ✓ Real-time
                  </span>
                </div>
                {scrapingMode.apiCommentsReceived > 0 && (
                  <div className="text-xs text-green-600 mt-1">
                    API Comments: {scrapingMode.apiCommentsReceived} |
                    Total Processed: {scrapingMode.totalProcessedComments}
                  </div>
                )}
              </div>
              <div className="text-xs text-green-600 font-medium">
                DOM Scraping đã bỏ
              </div>
            </div>
          </div>
        )}

        {/* Stats */}
        {systemState.isRunning && (
          <div className="bg-gray-50 rounded-lg p-4 mt-4">
            <h4 className="text-sm font-medium text-gray-900 mb-3">Thống kê</h4>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
              <div>
                <span className="text-gray-500">Tổng bình luận:</span>
                <div className="font-semibold text-gray-900">{systemState.totalComments}</div>
              </div>
              <div>
                <span className="text-gray-500">Đã xử lý:</span>
                <div className="font-semibold text-gray-900">{systemState.processedComments}</div>
              </div>
              <div>
                <span className="text-blue-500">Đã in:</span>
                <div className="font-semibold text-blue-600">{systemState.printedComments}</div>
              </div>
              <div>
                <span className="text-green-500">Tin nhắn đã gửi:</span>
                <div className="font-semibold text-green-600">{systemState.sentMessages}</div>
              </div>
              <div>
                <span className="text-gray-500">Lỗi:</span>
                <div className="font-semibold text-red-600">{systemState.errors}</div>
              </div>
            </div>
          </div>
        )}

        {/* Security Notice */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <AlertCircle className="h-5 w-5 text-yellow-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">Lưu ý bảo mật</h3>
              <div className="mt-2 text-sm text-yellow-700">
                <p>
                  Cookies đăng nhập được lưu trữ cục bộ để tiện lợi, giống như các website lớn.
                  Sau khi đăng nhập thành công, bạn có thể sử dụng nút "Xóa cookies" để xóa phiên đăng nhập.
                  Khuyến nghị sử dụng tài khoản phụ để đảm bảo an toàn.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Login Popup */}
      <LoginPopup
        isOpen={showLoginPopup}
        onClose={() => setShowLoginPopup(false)}
        onLogin={handlePopupLogin}
        title="Đăng nhập Instagram - Thu thập bình luận"
        isLoading={isStarting}
        credentialsType="scraper"
      />
    </div>
  );
};

export default ScrapingControl;

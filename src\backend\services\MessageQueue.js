const EventEmitter = require('events');
const winston = require('winston');
const axios = require('axios');

class MessageQueue extends EventEmitter {
  constructor() {
    super();
    this.messageQueue = [];
    this.isProcessing = false;
    this.useRedis = false; // Disable Redis by default
    this.logger = winston.createLogger({
      level: 'info',
      format: winston.format.simple(),
      transports: [new winston.transports.Console()]
    });
    this.retryAttempts = 3;
    this.retryDelay = 5000; // 5 seconds
    this.processingInterval = null;

    // Thêm thuộc tính để xác định phương thức gửi tin nhắn
    this.useInstagrapi = false;
    this.instagrapiMessenger = null; // Sẽ được set từ bên ngoài
  }

  async initialize() {
    try {
      this.logger.info('Initializing in-memory message queue...');

      // Use simple in-memory queue instead of Redis
      this.messageQueue = [];
      this.isProcessing = false;

      // Start processing loop
      this.startProcessing();

      this.logger.info('In-memory message queue initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize message queue:', error);
      throw error;
    }
  }

  // Thêm phương thức để set InstagrapiMessenger
  setInstagrapiMessenger(messenger) {
    this.instagrapiMessenger = messenger;
    return this;
  }

  // Thêm phương thức để chuyển đổi giữa các chế độ gửi tin nhắn
  setUseInstagrapi(useInstagrapi) {
    this.useInstagrapi = useInstagrapi;
    this.logger.info(`Message delivery method set to: ${useInstagrapi ? 'Instagrapi' : 'Chrome Testing'}`);
    return { status: 'success', useInstagrapi };
  }

  startProcessing() {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
    }

    this.processingInterval = setInterval(async () => {
      if (!this.isProcessing && this.messageQueue.length > 0) {
        // Dùng phương thức phù hợp với chế độ đã chọn
        if (this.useInstagrapi && this.instagrapiMessenger) {
          await this.processNextMessageInstagrapi();
        } else {
          await this.processNextMessage();
        }
      }
    }, 1000); // Check every second
  }

  async processNextMessage() {
    if (this.isProcessing || this.messageQueue.length === 0) {
      return;
    }

    this.isProcessing = true;
    const message = this.messageQueue.shift();

    try {
      this.logger.info(`Processing message: ${message.id}`);

      // Simulate processing (since we don't have actual Instagram messaging here)
      await new Promise(resolve => setTimeout(resolve, 2000));

      this.emit('message-sent', {
        jobId: message.id,
        username: message.username,
        message: message.message,
        result: 'success'
      });

      this.logger.info(`Message sent successfully: ${message.id}`);

    } catch (error) {
      this.logger.error(`Message failed: ${message.id}`, error);

      // Retry logic
      if (message.retries < this.retryAttempts) {
        message.retries++;
        this.messageQueue.push(message); // Add back to queue
        this.logger.info(`Retrying message: ${message.id} (${message.retries}/${this.retryAttempts})`);
      } else {
        this.emit('message-failed', {
          jobId: message.id,
          username: message.username,
          message: message.message,
          error: error.message
        });
      }
    } finally {
      this.isProcessing = false;
      this.emitQueueStats();
    }
  }

  // Thêm phương thức mới để xử lý tin nhắn bằng instagrapi
  async processNextMessageInstagrapi() {
    if (this.isProcessing || this.messageQueue.length === 0 || !this.instagrapiMessenger) {
      return;
    }

    this.isProcessing = true;
    const message = this.messageQueue.shift();

    try {
      this.logger.info(`Processing message with Instagrapi: ${message.id}`);

      // Gửi tin nhắn qua Instagrapi
      const result = await this.instagrapiMessenger.sendMessage(
        message.message,
        message.threadId,
        message.username
      );

      if (result.status === 'success') {
        // Lưu message_id và thread_id nếu cần thu hồi tin nhắn sau này
        const messageData = {
          jobId: message.id,
          username: message.username,
          message: message.message,
          messageId: result.message_id,
          threadId: result.thread_id,
          result: 'success'
        };

        this.emit('message-sent', messageData);
        this.logger.info(`Message sent successfully via Instagrapi: ${message.id}`);
      } else {
        throw new Error(result.message || 'Gửi tin nhắn thất bại');
      }
    } catch (error) {
      this.logger.error(`Message failed (Instagrapi): ${message.id}`, error);

      // Retry logic
      if (message.retries < this.retryAttempts) {
        message.retries++;
        this.messageQueue.push(message); // Add back to queue
        this.logger.info(`Retrying message: ${message.id} (${message.retries}/${this.retryAttempts})`);
      } else {
        this.emit('message-failed', {
          jobId: message.id,
          username: message.username,
          message: message.message,
          error: error.message
        });
      }
    } finally {
      this.isProcessing = false;
      this.emitQueueStats();

      // Đợi 10 giây trước khi xử lý tin nhắn tiếp theo
      await new Promise(resolve => setTimeout(resolve, 10000));
    }
  }

  emitQueueStats() {
    const stats = {
      waiting: this.messageQueue.length,
      active: this.isProcessing ? 1 : 0,
      completed: 0, // We don't track this in memory
      failed: 0, // We don't track this in memory
      delayed: 0
    };

    this.emit('queue-updated', stats);
  }

  async addMessage(messageData) {
    try {
      const { username, message, template, variables, threadId } = messageData;

      if (!username || (!message && !template)) {
        throw new Error('Username and message/template are required');
      }

      // Process message template if provided
      let finalMessage = message;
      if (template && variables) {
        finalMessage = this.processTemplate(template, variables);
      }

      const messageObj = {
        id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        username,
        message: finalMessage,
        threadId, // Thêm thread_id nếu có
        timestamp: new Date().toISOString(),
        retries: 0,
        ...messageData
      };

      // Always add to end of queue (FIFO)
      this.messageQueue.push(messageObj);

      this.logger.info(`📝 Message queued (FIFO) for ${username}: ${messageObj.id}`);
      this.emitQueueStats();

      return messageObj;
    } catch (error) {
      this.logger.error('Failed to add message to queue:', error);
      throw error;
    }
  }

  processTemplate(template, variables) {
    let processedMessage = template;

    // Replace variables in template
    Object.keys(variables).forEach(key => {
      const placeholder = `{{${key}}}`;
      processedMessage = processedMessage.replace(new RegExp(placeholder, 'g'), variables[key]);
    });

    return processedMessage;
  }

  async getQueueStats() {
    return {
      waiting: this.messageQueue.length,
      active: this.isProcessing ? 1 : 0,
      completed: 0,
      failed: 0,
      jobs: {
        waiting: this.messageQueue.map(msg => ({
          id: msg.id,
          data: { username: msg.username, message: msg.message },
          timestamp: msg.timestamp
        })),
        active: []
      }
    };
  }

  async pauseQueue() {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
    }
    this.logger.info('Message queue paused');
  }

  async resumeQueue() {
    this.startProcessing();
    this.logger.info('Message queue resumed');
  }

  async clearQueue() {
    this.messageQueue = [];
    this.logger.info('Message queue cleared');
  }

  async close() {
    try {
      if (this.processingInterval) {
        clearInterval(this.processingInterval);
        this.processingInterval = null;
      }

      this.messageQueue = [];
      this.isProcessing = false;

      this.logger.info('Message queue closed');
    } catch (error) {
      this.logger.error('Error closing message queue:', error);
    }
  }

  // Thêm phương thức để unsend tin nhắn
  async unsendMessage(threadId, messageId) {
    if (!this.instagrapiMessenger) {
      return { status: 'error', message: 'InstagrapiMessenger chưa được khởi tạo' };
    }

    return await this.instagrapiMessenger.unsendMessage(threadId, messageId);
  }
}

module.exports = MessageQueue;

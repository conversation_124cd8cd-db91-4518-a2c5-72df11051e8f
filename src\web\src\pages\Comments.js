import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  MessageCircle,
  Trash2,
  Play,
  Loader,
  AlertCircle,
  LogIn,
  CheckCircle
} from 'lucide-react';
import { useSocket } from '../contexts/SocketContext';
import { useApp } from '../contexts/AppContext';
import CommentItem from '../components/CommentItem';
import LoginPopup from '../components/LoginPopup';
import { getApiUrl } from '../config/api';
import MessageQueueManager from '../components/MessageQueueManager';
import credentialsService from '../services/credentialsService';

// moment import moved to CommentItem component
import toast from 'react-hot-toast';

// Helper function to ensure getApiUrl is available
const safeGetApiUrl = (endpoint) => {
  try {
    return getApiUrl(endpoint);
  } catch (error) {
    console.error('getApiUrl error:', error);
    // Fallback for production builds
    const hostname = window.location.hostname;
    const port = hostname === 'localhost' || hostname === '127.0.0.1' ? '3001' : '3001';
    const baseUrl = `http://${hostname}:${port}`;
    return `${baseUrl}${endpoint}`;
  }
};

const Comments = () => {
  const { displayedComments, loadMoreComments, recentComments, printComment, isConnected, clearSessionComments, socket, systemState } = useSocket();
  const { actions } = useApp();

  const [regularCustomers, setRegularCustomers] = useState(new Set());
  const [lastRefresh, setLastRefresh] = useState(Date.now()); // For forcing re-renders

  // Biến theo dõi việc loading thêm comments
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  // Sentinel ref để theo dõi khi cần load thêm comments
  const commentsLoadingRef = useRef(null);
  // Observer để phát hiện khi cần load thêm
  const observer = useRef(null);

  // Kiểm tra xem có đủ comments để có thể load thêm hay không
  const hasEnoughCommentsToLoad = recentComments.length >= 100;

  // Quick start states
  const [quickStartData, setQuickStartData] = useState({
    liveUsername: '',
    scraperCookies: false,
    messengerCookies: false,
    scraperRunning: false,
    messengerRunning: false,
    isStartingScraper: false,
    isStartingMessenger: false,
    // Instagrapi states
    useInstagrapi: false,
    instagrapiConnected: false,
    instagrapiRunning: false
  });

  // Login popup states
  const [showScraperLoginPopup, setShowScraperLoginPopup] = useState(false);
  const [showMessengerLoginPopup, setShowMessengerLoginPopup] = useState(false);

  // Saved credentials states
  const [hasSavedScraperCredentials, setHasSavedScraperCredentials] = useState(false);
  const [hasSavedMessengerCredentials, setHasSavedMessengerCredentials] = useState(false);
  const [hasSavedInstagrapiCredentials, setHasSavedInstagrapiCredentials] = useState(false);
  const [userInteracting, setUserInteracting] = useState(false);
  const [isTouching, setIsTouching] = useState(false);
  const [isHolding, setIsHolding] = useState(false);
  const [isScrolling, setIsScrolling] = useState(false);
  const [colorSettings, setColorSettings] = useState({
    newCustomer: {
      background: '#f3f4f6',
      border: '#d1d5db',
      username: '#7c3aed',
      text: '#374151'
    },
    regularCustomer: {
      background: '#fef3c7',
      border: '#f59e0b',
      username: '#d97706',
      text: '#374151'
    }
  });
  const commentsEndRef = useRef(null);
  const interactionTimeoutRef = useRef(null);
  const lastTouchEndRef = useRef(null);
  const touchSequenceCountRef = useRef(0);
  const holdTimeoutRef = useRef(null);
  const scrollTimeoutRef = useRef(null);
  const lastScrollTimeRef = useRef(null);

  // Detect if device is mobile
  const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);

  // Thêm useCallback để tạo observer theo dõi khi cần load thêm comments
  const handleObserver = useCallback((entries) => {
    const [entry] = entries;
    // Khi sentinel element hiển thị (gần đầu container)
    if (entry.isIntersecting && !isLoadingMore && hasEnoughCommentsToLoad) {
      setIsLoadingMore(true);
      console.log('📜 Sentinel element visible, loading more comments...');

      // Gọi hàm loadMoreComments từ SocketContext
      setTimeout(() => {
        const hasMore = loadMoreComments();
        console.log(`📜 Load more result: ${hasMore ? 'Loaded more comments' : 'No more comments to load'}`);
        setIsLoadingMore(false);

        if (!hasMore) {
          // Nếu không còn comments để load, hủy observer
          console.log('📜 No more comments to load, disconnecting observer');
          if (observer.current) {
            observer.current.disconnect();
          }
        }
      }, 500); // Thêm timeout nhỏ để UX tốt hơn
    }
  }, [isLoadingMore, loadMoreComments, hasEnoughCommentsToLoad]);

  // Setup observer
  useEffect(() => {
    const options = {
      root: null, // viewport
      rootMargin: '0px',
      threshold: 0.1, // Trigger khi chỉ cần hiển thị 10% của element
    };

    observer.current = new IntersectionObserver(handleObserver, options);

    if (commentsLoadingRef.current) {
      observer.current.observe(commentsLoadingRef.current);
      console.log('📜 Observer attached to sentinel element');
    }

    return () => {
      if (observer.current) {
        observer.current.disconnect();
        console.log('📜 Observer disconnected');
      }
    };
  }, [handleObserver]);

  // Check for saved credentials
  const checkSavedCredentials = async () => {
    try {
      // Check server APIs for all services
      let scraperCreds = false;
      let messengerCreds = false;
      let instagrapiCreds = false;

      // Check scraper cookies from server
      try {
        const response = await fetch(safeGetApiUrl('/api/saved-cookies'));
        const data = await response.json();
        scraperCreds = data.success && data.hasCookies;
      } catch (error) {
        console.error('Error checking scraper credentials:', error);
      }

      // Check messenger cookies from server
      try {
        const response = await fetch(safeGetApiUrl('/api/messenger-saved-cookies'));
        const data = await response.json();
        messengerCreds = data.success && data.hasCookies;
      } catch (error) {
        console.error('Error checking messenger credentials:', error);
      }

      // Check instagrapi credentials from server
      try {
        const response = await fetch(safeGetApiUrl('/api/instagrapi/credentials'));
        const data = await response.json();
        instagrapiCreds = data.success && data.hasCredentials;
      } catch (error) {
        console.error('Error checking instagrapi credentials:', error);
      }

      console.log('🔐 Checking saved credentials from server:', {
        scraper: scraperCreds,
        messenger: messengerCreds,
        instagrapi: instagrapiCreds
      });

      setHasSavedScraperCredentials(scraperCreds);
      setHasSavedMessengerCredentials(messengerCreds);
      setHasSavedInstagrapiCredentials(instagrapiCreds);
    } catch (error) {
      console.error('Error checking saved credentials:', error);
    }
  };

  // Debug: Log displayedComments changes
  useEffect(() => {
    console.log('=== COMMENTS PAGE: displayedComments updated ===');
    console.log('Comments count:', displayedComments.length);
  }, [displayedComments]);

  useEffect(() => {
    const initializeData = async () => {
      actions.setCurrentPage('comments');
      loadRegularCustomers();
      checkCookieStatus();
      await checkSavedCredentials();
    };
    initializeData();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // Refresh cookie status and service status when system state changes
  useEffect(() => {
    checkCookieStatus();
    checkServiceStatus();
  }, [systemState?.isRunning, systemState?.messengerConnected]);

  // Update service running status from system state
  useEffect(() => {
    setQuickStartData(prev => ({
      ...prev,
      scraperRunning: systemState?.isRunning || false,
      messengerRunning: systemState?.messengerConnected || false
    }));
  }, [systemState?.isRunning, systemState?.messengerConnected]);

  // Load color settings from localStorage on mount
  useEffect(() => {
    const savedColors = localStorage.getItem('instagram-live-color-settings');
    if (savedColors) {
      try {
        const parsedColors = JSON.parse(savedColors);
        setColorSettings(parsedColors);
      } catch (error) {
        console.error('Failed to parse saved color settings:', error);
      }
    }

    // Listen for color settings changes
    const handleColorSettingsChange = (event) => {
      setColorSettings(event.detail);
    };

    window.addEventListener('colorSettingsChanged', handleColorSettingsChange);
    return () => {
      window.removeEventListener('colorSettingsChanged', handleColorSettingsChange);
    };
  }, []);



  // Socket listeners for regular customers sync
  useEffect(() => {
    if (!socket) return;

    const handleRegularCustomerAdded = (data) => {
      setRegularCustomers(prev => {
        const newSet = new Set(prev);
        newSet.add(data.username);
        return newSet;
      });
    };

    const handleRegularCustomerRemoved = (data) => {
      setRegularCustomers(prev => {
        const newSet = new Set(prev);
        newSet.delete(data.username);
        return newSet;
      });
    };

    socket.on('regular-customer-added', handleRegularCustomerAdded);
    socket.on('regular-customer-removed', handleRegularCustomerRemoved);

    return () => {
      socket.off('regular-customer-added', handleRegularCustomerAdded);
      socket.off('regular-customer-removed', handleRegularCustomerRemoved);
    };
  }, [socket]);

  // Load regular customers from API
  const loadRegularCustomers = async () => {
    try {
      const response = await fetch(safeGetApiUrl('/api/regular-customers'));
      const data = await response.json();

      if (data.success) {
        const customerSet = new Set(data.customers.map(c => c.username));
        setRegularCustomers(customerSet);
      }
    } catch (error) {
      console.error('Failed to load regular customers:', error);
    }
  };

  // Check cookie status for quick start
  const checkCookieStatus = async () => {
    try {
      // Check scraper cookies
      const scraperResponse = await fetch(safeGetApiUrl('/api/saved-cookies'));
      const scraperData = await scraperResponse.json();

      // Check messenger cookies
      const messengerResponse = await fetch(safeGetApiUrl('/api/messenger-saved-cookies'));
      const messengerData = await messengerResponse.json();

      setQuickStartData(prev => ({
        ...prev,
        scraperCookies: scraperData.success && scraperData.hasCookies,
        messengerCookies: messengerData.success && messengerData.hasCookies
      }));
    } catch (error) {
      console.error('Failed to check cookie status:', error);
    }
  };

  // Check service running status
  const checkServiceStatus = async () => {
    try {
      // Check scraper status
      const scraperResponse = await fetch(safeGetApiUrl('/api/scraper-status'));
      const scraperData = await scraperResponse.json();

      // Check messenger mode
      const modeResponse = await fetch(safeGetApiUrl('/api/messenger/get-mode'));
      const modeData = await modeResponse.json();
      const useInstagrapi = modeData.success ? modeData.useInstagrapi : false;

      if (useInstagrapi) {
        // Check Instagrapi status
        const instagrapiResponse = await fetch(safeGetApiUrl('/api/messenger/instagrapi/status'));
        const instagrapiData = await instagrapiResponse.json();

        setQuickStartData(prev => ({
          ...prev,
          scraperRunning: scraperData.success && scraperData.isRunning,
          useInstagrapi: true,
          instagrapiConnected: instagrapiData.success && instagrapiData.status?.isLoggedIn || false,
          instagrapiRunning: instagrapiData.success && instagrapiData.status?.isRunning || false,
          messengerRunning: false // Reset Chrome Testing status
        }));
      } else {
        // Check Chrome Testing messenger status
        const messengerResponse = await fetch(safeGetApiUrl('/api/messenger-status'));
        const messengerData = await messengerResponse.json();

        setQuickStartData(prev => ({
          ...prev,
          scraperRunning: scraperData.success && scraperData.isRunning,
          useInstagrapi: false,
          messengerRunning: messengerData.success && messengerData.isRunning,
          instagrapiConnected: false, // Reset Instagrapi status
          instagrapiRunning: false
        }));
      }
    } catch (error) {
      console.error('Failed to check service status:', error);
    }
  };

  // Quick start scraper
  const handleQuickStartScraper = async () => {
    if (!quickStartData.liveUsername.trim()) {
      toast.error('Vui lòng nhập tên Instagram Live');
      return;
    }

    setQuickStartData(prev => ({ ...prev, isStartingScraper: true }));

    try {
      const response = await fetch(safeGetApiUrl('/api/start-scraping'), {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          liveUsername: quickStartData.liveUsername.trim(),
          useSavedCookies: true
        })
      });

      const data = await response.json();
      if (data.success) {
        toast.success('Đã khởi động thu thập bình luận!');
      } else {
        throw new Error(data.error || 'Failed to start scraper');
      }
    } catch (error) {
      console.error('Failed to start scraper:', error);
      toast.error(`Lỗi khởi động thu thập bình luận: ${error.message}`);
    } finally {
      setQuickStartData(prev => ({ ...prev, isStartingScraper: false }));
    }
  };

  // Quick start messenger
  const handleQuickStartMessenger = async () => {
    if (quickStartData.useInstagrapi) {
      // Handle Instagrapi quick start
      return handleQuickStartInstagrapi();
    }

    // Handle Chrome Testing messenger quick start
    setQuickStartData(prev => ({ ...prev, isStartingMessenger: true }));

    try {
      const response = await fetch(safeGetApiUrl('/api/start-messenger'), {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          useSavedCookies: true
        })
      });

      const data = await response.json();
      if (data.success) {
        toast.success('Đã khởi động tin nhắn tự động!');
        await checkServiceStatus();
      } else {
        throw new Error(data.error || 'Failed to start messenger');
      }
    } catch (error) {
      console.error('Failed to start messenger:', error);
      toast.error(`Lỗi khởi động messenger: ${error.message}`);
    } finally {
      setQuickStartData(prev => ({ ...prev, isStartingMessenger: false }));
    }
  };

  // Quick start Instagrapi
  const handleQuickStartInstagrapi = async () => {
    setQuickStartData(prev => ({ ...prev, isStartingMessenger: true }));

    try {
      // Check if we have saved credentials
      const credentialsResponse = await fetch(safeGetApiUrl('/api/instagrapi/credentials'));
      const credentialsData = await credentialsResponse.json();

      if (credentialsData.hasCredentials) {
        // Auto-login with saved credentials
        const response = await fetch(safeGetApiUrl('/api/messenger/instagrapi/auto-login'), {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' }
        });

        const data = await response.json();
        if (data.success) {
          // Start queue processor
          const startResponse = await fetch(safeGetApiUrl('/api/messenger/instagrapi/start'), {
            method: 'POST'
          });

          const startData = await startResponse.json();
          if (startData.success) {
            toast.success('Đã khởi động Instagrapi với thông tin đăng nhập đã lưu');
            await checkServiceStatus();
          } else {
            // Check if it's already processing (not an error)
            if (startData.message && startData.message.includes('Dang xu ly hang doi')) {
              toast.success('Instagrapi đã sẵn sàng và đang xử lý hàng đợi tin nhắn');
              await checkServiceStatus();
            } else {
              throw new Error(startData.message || 'Failed to start Instagrapi processor');
            }
          }
        } else {
          throw new Error(data.message || 'Failed to auto-login to Instagrapi');
        }
      } else {
        // No saved credentials, show login popup
        setShowMessengerLoginPopup(true);
      }
    } catch (error) {
      console.error('Failed to start Instagrapi:', error);
      toast.error(`Lỗi khởi động Instagrapi: ${error.message}`);
    } finally {
      setQuickStartData(prev => ({ ...prev, isStartingMessenger: false }));
    }
  };

  // Handle scraper login/start from popup
  const handleScraperPopupLogin = async (loginCredentials) => {
    // If cookies exist but service not running, just start with saved cookies
    if (quickStartData.scraperCookies && !quickStartData.scraperRunning) {
      if (!quickStartData.liveUsername.trim()) {
        toast.error('Vui lòng nhập tên Instagram Live');
        return;
      }

      setQuickStartData(prev => ({ ...prev, isStartingScraper: true }));
      try {
        const response = await fetch(safeGetApiUrl('/api/start-scraping'), {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            liveUsername: quickStartData.liveUsername.trim(),
            useSavedCookies: true
          })
        });

        const data = await response.json();
        if (data.success) {
          toast.success('Đã khởi động thu thập bình luận với phiên đăng nhập đã lưu');
          setShowScraperLoginPopup(false);
          await checkServiceStatus();
        } else {
          throw new Error(data.error || 'Failed to start scraper');
        }
      } catch (error) {
        console.error('Failed to start scraper:', error);
        toast.error(`Lỗi khởi động thu thập bình luận: ${error.message}`);
      } finally {
        setQuickStartData(prev => ({ ...prev, isStartingScraper: false }));
      }
      return;
    }

    // If no cookies, need to login first
    if (!quickStartData.liveUsername.trim()) {
      toast.error('Vui lòng nhập tên Instagram Live');
      return;
    }

    setQuickStartData(prev => ({ ...prev, isStartingScraper: true }));
    try {
      const response = await fetch(safeGetApiUrl('/api/start-scraping'), {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          liveUsername: quickStartData.liveUsername.trim(),
          credentials: {
            username: loginCredentials.username.trim(),
            password: loginCredentials.password.trim()
          },
          useSavedCookies: false,
          saveCredentials: loginCredentials.saveCredentials || false
        })
      });

      const data = await response.json();
      if (data.success) {
        const message = loginCredentials.saveCredentials
          ? 'Đã bắt đầu thu thập bình luận - Phiên đăng nhập đã được lưu'
          : 'Đã bắt đầu thu thập bình luận';
        toast.success(message);
        setShowScraperLoginPopup(false);
        // Reload cookie and service status
        await checkCookieStatus();
        await checkServiceStatus();
        // Refresh saved credentials status
        await checkSavedCredentials();
      } else {
        throw new Error(data.error || 'Failed to start scraper');
      }
    } catch (error) {
      console.error('Failed to start scraper:', error);
      toast.error(`Lỗi khởi động thu thập bình luận: ${error.message}`);
    } finally {
      setQuickStartData(prev => ({ ...prev, isStartingScraper: false }));
    }
  };

  // Handle messenger login/start from popup
  const handleMessengerPopupLogin = async (loginCredentials) => {
    if (quickStartData.useInstagrapi) {
      // Handle Instagrapi login
      return handleInstagrapiPopupLogin(loginCredentials);
    }

    // Handle Chrome Testing messenger login
    // If cookies exist but service not running, just start with saved cookies
    if (quickStartData.messengerCookies && !quickStartData.messengerRunning) {
      setQuickStartData(prev => ({ ...prev, isStartingMessenger: true }));
      try {
        const response = await fetch(safeGetApiUrl('/api/start-messenger'), {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            useSavedCookies: true
          })
        });

        const data = await response.json();
        if (data.success) {
          toast.success('Đã khởi động tin nhắn tự động với phiên đăng nhập đã lưu');
          setShowMessengerLoginPopup(false);
          await checkServiceStatus();
        } else {
          throw new Error(data.error || 'Failed to start messenger');
        }
      } catch (error) {
        console.error('Failed to start messenger:', error);
        toast.error(`Lỗi khởi động tin nhắn tự động: ${error.message}`);
      } finally {
        setQuickStartData(prev => ({ ...prev, isStartingMessenger: false }));
      }
      return;
    }

    // If no cookies, need to login first
    setQuickStartData(prev => ({ ...prev, isStartingMessenger: true }));
    try {
      const response = await fetch(safeGetApiUrl('/api/start-messenger'), {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          credentials: {
            username: loginCredentials.username.trim(),
            password: loginCredentials.password.trim()
          },
          useSavedCookies: false,
          saveCredentials: loginCredentials.saveCredentials || false
        })
      });

      const data = await response.json();
      if (data.pending2FA) {
        toast.error('Cần xác thực 2FA! Vui lòng nhập mã xác thực trong cửa sổ trình duyệt.', {
          duration: 10000,
          icon: '🔐'
        });
        setQuickStartData(prev => ({ ...prev, isStartingMessenger: false }));
        return;
      }

      if (data.success) {
        const message = loginCredentials.saveCredentials
          ? 'Đã bắt đầu Instagram Messenger - Phiên đăng nhập đã được lưu'
          : 'Đã bắt đầu Instagram Messenger';
        toast.success(message);
        setShowMessengerLoginPopup(false);
        // Reload cookie and service status
        await checkCookieStatus();
        await checkServiceStatus();
        // Refresh saved credentials status
        await checkSavedCredentials();
      } else {
        throw new Error(data.error || 'Failed to start messenger');
      }
    } catch (error) {
      console.error('Failed to start messenger:', error);
      toast.error(`Lỗi khởi động tin nhắn tự động: ${error.message}`);
    } finally {
      setQuickStartData(prev => ({ ...prev, isStartingMessenger: false }));
    }
  };

  // Handle Instagrapi login from popup
  const handleInstagrapiPopupLogin = async (loginCredentials) => {
    setQuickStartData(prev => ({ ...prev, isStartingMessenger: true }));

    try {
      // If already connected but not running, just start the processor
      if (quickStartData.instagrapiConnected && !quickStartData.instagrapiRunning) {
        const startResponse = await fetch(safeGetApiUrl('/api/messenger/instagrapi/start'), {
          method: 'POST'
        });

        const startData = await startResponse.json();
        if (startData.success) {
          toast.success('Đã khởi động Instagrapi thành công');
          setShowMessengerLoginPopup(false);
          await checkServiceStatus();
          return;
        } else {
          // Check if it's already processing (not an error)
          if (startData.message && startData.message.includes('Dang xu ly hang doi')) {
            toast.success('Instagrapi đã sẵn sàng và đang xử lý hàng đợi tin nhắn');
            setShowMessengerLoginPopup(false);
            await checkServiceStatus();
            return;
          } else {
            throw new Error(startData.message || 'Failed to start Instagrapi processor');
          }
        }
      }

      // Need to login first
      const response = await fetch(safeGetApiUrl('/api/messenger/instagrapi/login'), {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          username: loginCredentials.username.trim(),
          password: loginCredentials.password.trim(),
          saveCredentials: loginCredentials.saveCredentials !== undefined ? loginCredentials.saveCredentials : true
        })
      });

      const data = await response.json();
      if (data.success) {
        // Start queue processor
        const startResponse = await fetch(safeGetApiUrl('/api/messenger/instagrapi/start'), {
          method: 'POST'
        });

        const startData = await startResponse.json();
        if (startData.success) {
          const message = loginCredentials.saveCredentials !== false
            ? 'Đã đăng nhập và khởi động Instagrapi thành công - Thông tin đăng nhập đã được lưu'
            : 'Đã đăng nhập và khởi động Instagrapi thành công';
          toast.success(message);
          setShowMessengerLoginPopup(false);
          await checkServiceStatus();
          // Refresh saved credentials status
          await checkSavedCredentials();
        } else {
          // Check if it's already processing (not an error)
          if (startData.message && startData.message.includes('Dang xu ly hang doi')) {
            const message = loginCredentials.saveCredentials !== false
              ? 'Đã đăng nhập Instagrapi thành công và đang xử lý hàng đợi tin nhắn - Thông tin đăng nhập đã được lưu'
              : 'Đã đăng nhập Instagrapi thành công và đang xử lý hàng đợi tin nhắn';
            toast.success(message);
            setShowMessengerLoginPopup(false);
            await checkServiceStatus();
            // Refresh saved credentials status
            await checkSavedCredentials();
          } else {
            throw new Error(startData.message || 'Failed to start Instagrapi processor');
          }
        }
      } else {
        throw new Error(data.message || 'Failed to login to Instagrapi');
      }
    } catch (error) {
      console.error('Failed to login/start Instagrapi:', error);
      toast.error(`Lỗi đăng nhập/khởi động Instagrapi: ${error.message}`);
    } finally {
      setQuickStartData(prev => ({ ...prev, isStartingMessenger: false }));
    }
  };

  // Auto scroll to bottom when new comments arrive
  useEffect(() => {
    const shouldAutoScroll = displayedComments.length >= 7 && !userInteracting && !isTouching && !isScrolling;

    // On mobile, also check isHolding state
    const mobileHoldCheck = isMobile ? !isHolding : true;

    if (shouldAutoScroll && mobileHoldCheck) {
      scrollToBottom();
    }
  }, [displayedComments, userInteracting, isTouching, isHolding, isScrolling, isMobile]);

  // Handle scroll events (including momentum scrolling)
  const handleScroll = () => {
    setIsScrolling(true);
    setUserInteracting(true);
    lastScrollTimeRef.current = Date.now();

    // Clear existing timeouts
    if (interactionTimeoutRef.current) {
      clearTimeout(interactionTimeoutRef.current);
    }
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    // Detect when scrolling stops (including momentum)
    scrollTimeoutRef.current = setTimeout(() => {
      setIsScrolling(false);

      // Start main timeout after scrolling completely stops
      interactionTimeoutRef.current = setTimeout(() => {
        setUserInteracting(false);
      }, isMobile ? 3500 : 2000);
    }, 150); // 150ms to detect scroll stop
  };

  // Handle user interaction detection (for non-scroll events)
  const handleUserInteraction = () => {
    setUserInteracting(true);

    // Clear existing timeout
    if (interactionTimeoutRef.current) {
      clearTimeout(interactionTimeoutRef.current);
    }

    // Set timeout to resume auto scroll
    interactionTimeoutRef.current = setTimeout(() => {
      setUserInteracting(false);
    }, 2000);
  };

  // Handle touch events
  const handleTouchStart = () => {
    setIsTouching(true);
    setUserInteracting(true);

    // Clear existing timeouts
    if (interactionTimeoutRef.current) {
      clearTimeout(interactionTimeoutRef.current);
    }
    if (holdTimeoutRef.current) {
      clearTimeout(holdTimeoutRef.current);
    }

    // Only set hold detection on mobile devices
    if (isMobile) {
      holdTimeoutRef.current = setTimeout(() => {
        setIsHolding(true);
      }, 500);
    }
  };

  const handleTouchEnd = () => {
    setIsTouching(false);

    // Clear hold timeout
    if (holdTimeoutRef.current) {
      clearTimeout(holdTimeoutRef.current);
    }

    if (isMobile) {
      setIsHolding(false);
    }

    // On mobile, don't start timeout immediately after touchEnd
    // Let the scroll handler detect when momentum scrolling stops
    if (!isMobile) {
      const now = Date.now();
      const timeSinceLastTouch = lastTouchEndRef.current ? now - lastTouchEndRef.current : Infinity;

      // Desktop logic remains the same
      if (timeSinceLastTouch < 3000) {
        touchSequenceCountRef.current += 1;
      } else {
        touchSequenceCountRef.current = 1;
      }

      lastTouchEndRef.current = now;

      let timeoutDuration = 2000;
      if (touchSequenceCountRef.current > 1) {
        timeoutDuration = 5000;
      }

      interactionTimeoutRef.current = setTimeout(() => {
        setUserInteracting(false);
        touchSequenceCountRef.current = 0;
      }, timeoutDuration);
    }
    // Mobile timeout is handled by scroll handler to account for momentum
  };

  const scrollToBottom = () => {
    commentsEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };



  const toggleRegularCustomer = async (username) => {
    try {
      const isCurrentlyRegular = regularCustomers.has(username);

      if (isCurrentlyRegular) {
        // Remove from regular customers
        const response = await fetch(safeGetApiUrl(`/api/regular-customers/${username}`), {
          method: 'DELETE'
        });

        if (response.ok) {
          // Local update will be handled by socket event
          console.log(`Đã bỏ đánh dấu khách quen: @${username}`);

          // Emit socket event to sync with other clients
          if (socket) {
            socket.emit('regular-customer-removed', { username });
          }
        }
      } else {
        // Add to regular customers
        const response = await fetch(safeGetApiUrl('/api/regular-customers'), {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ username })
        });

        if (response.ok) {
          // Local update will be handled by socket event
          console.log(`Đã đánh dấu khách quen: @${username}`);

          // Emit socket event to sync with other clients
          if (socket) {
            socket.emit('regular-customer-added', { username });
          }
        }
      }
    } catch (error) {
      console.error('Failed to toggle regular customer:', error);
    }
  };

  // REMOVED: handleBackupClick - now handled by BackupButton component

  // Thêm state lưu trạng thái hàng chờ cho từng comment
  const [messageQueue, setMessageQueue] = useState([]);
  const [commentQueueMap, setCommentQueueMap] = useState({}); // { commentId: queueMessageId }

  // State để track comment đang được xử lý
  const [processingComments, setProcessingComments] = useState(new Set());

  // Lắng nghe print-success và message-queued để map commentId <-> queueId
  useEffect(() => {
    if (!socket) return;

    const handlePrintProcessing = (data) => {
      if (data && data.commentId) {
        setProcessingComments(prev => new Set([...prev, data.commentId]));
      }
    };

    const handlePrintSuccess = (data) => {
      if (data && data.commentId && data.messageId) {
        setCommentQueueMap(prev => ({ ...prev, [data.commentId]: data.messageId }));
        console.log('Print success - mapping comment to queue:', data.commentId, '->', data.messageId);
        // Remove from processing set
        setProcessingComments(prev => {
          const newSet = new Set(prev);
          newSet.delete(data.commentId);
          return newSet;
        });

        // Immediately reload queue to show new message
        setTimeout(() => {
          fetchQueue();
        }, 100);
      }
    };

    const handlePrintError = (data) => {
      if (data && data.commentId) {
        // Remove from processing set on error
        setProcessingComments(prev => {
          const newSet = new Set(prev);
          newSet.delete(data.commentId);
          return newSet;
        });
      }
    };
    const handleBackupSuccess = (data) => {
      if (data && data.commentId && data.messageId) {
        setCommentQueueMap(prev => ({ ...prev, [data.commentId]: data.messageId }));
        console.log('Backup success - mapping comment to queue:', data.commentId, '->', data.messageId);
        // Remove from processing set
        setProcessingComments(prev => {
          const newSet = new Set(prev);
          newSet.delete(data.commentId);
          return newSet;
        });
      }
    };
    const handleMessageQueued = (data) => {
      if (data && data.messageId && data.username) {
        console.log('Message queued:', data);
        // Có thể map bằng username nếu cần
      }
    };
    socket.on('print-processing', handlePrintProcessing);
    socket.on('print-success', handlePrintSuccess);
    socket.on('print-error', handlePrintError);
    socket.on('backup-success', handleBackupSuccess);
    socket.on('message-queued', handleMessageQueued);
    return () => {
      socket.off('print-processing', handlePrintProcessing);
      socket.off('print-success', handlePrintSuccess);
      socket.off('print-error', handlePrintError);
      socket.off('backup-success', handleBackupSuccess);
      socket.off('message-queued', handleMessageQueued);
    };
  }, [socket]);

  // Load message queue khi mount và khi có update
  const fetchQueue = async () => {
    try {
      const res = await fetch(safeGetApiUrl('/api/message-queue?status=all'));
      const data = await res.json();
      if (data.success) {
        setMessageQueue(data.messages);
        console.log('Message queue updated:', data.messages.length, 'messages');
      }
    } catch (e) {
      console.error('Failed to fetch message queue:', e);
    }
  };

  useEffect(() => {
    fetchQueue();
  }, []);

  // Listen for message queue updates
  useEffect(() => {
    const handleQueueUpdate = () => {
      console.log('Received message-queue-updated event, reloading...');
      fetchQueue();
    };

    window.addEventListener('message-queue-updated', handleQueueUpdate);

    return () => {
      window.removeEventListener('message-queue-updated', handleQueueUpdate);
    };
  }, []);

  // Debug: Log when messageQueue changes
  useEffect(() => {
    console.log('🔄 MessageQueue state updated:', messageQueue.length, 'messages');
    messageQueue.forEach(msg => {
      if (['pending', 'processing', 'completed'].includes(msg.status)) {
        console.log(`- ${msg.id}: ${msg.username} (${msg.status})`);
      }
    });

    // Update lastRefresh to trigger re-render of comment colors
    setLastRefresh(Date.now());
  }, [messageQueue]);

  // Map trạng thái hàng chờ vào từng comment (ưu tiên mapping commentId <-> queueId)
  function getQueueStatusForComment(comment) {
    if (!comment) return null;
    // Nếu có mapping, tìm queue theo queueId
    const queueId = commentQueueMap[comment.id];
    let found = null;
    if (queueId) {
      found = messageQueue.find(m => m.id === queueId);
    }
    // Nếu không có mapping, fallback match comment_id
    if (!found) {
      found = messageQueue.find(m => m.comment_id === comment.id);
    }
    if (!found) {
      // Fallback: match username + text
      found = messageQueue.find(m => m.username === comment.username && m.original_comment === comment.text);
    }

    // Debug logging for troubleshooting
    if (found && ['pending', 'processing', 'completed'].includes(found.status)) {
      console.log(`🎨 Queue status for comment ${comment.id} (@${comment.username}): ${found.status} (messageId: ${found.id})`);
    }

    return found ? found.status : null;
  }

  function getQueueMetaForComment(comment) {
    if (!comment) return {};
    const queueId = commentQueueMap[comment.id];
    let found = null;
    if (queueId) {
      found = messageQueue.find(m => m.id === queueId);
    }
    if (!found) {
      found = messageQueue.find(m => m.comment_id === comment.id);
    }
    if (!found) {
      found = messageQueue.find(m => m.username === comment.username && m.original_comment === comment.text);
    }
    return found || {};
  }

  useEffect(() => {
    if (!socket) return;
    // Khi có message-status-update thì cập nhật trạng thái messageQueue
    const handleStatusUpdate = (data) => {
      console.log('🔄 Message status update received:', data);
      setMessageQueue(prev => {
        const oldQueue = prev.find(msg => msg.id === data.id);
        if (oldQueue) {
          console.log(`🎨 Color update: Message ${data.id} status ${oldQueue.status} → ${data.status}`);
        }
        return prev.map(msg =>
          msg.id === data.id ? { ...msg, status: data.status } : msg
        );
      });
      // Force re-render để cập nhật màu ngay lập tức
      setLastRefresh(Date.now());
    };

    // Lắng nghe cả hai tên event để đảm bảo không bỏ sót
    const handleStatusUpdateAlt = (data) => {
      console.log('🔄 Message status update (alt) received:', data);
      setMessageQueue(prev => {
        const oldQueue = prev.find(msg => msg.id === data.id);
        if (oldQueue) {
          console.log(`🎨 Color update (alt): Message ${data.id} status ${oldQueue.status} → ${data.status}`);
        }
        return prev.map(msg =>
          msg.id === data.id ? { ...msg, status: data.status } : msg
        );
      });
      // Force re-render để cập nhật màu ngay lập tức
      setLastRefresh(Date.now());
    };

    // Khi có message-queue-updated thì fetch lại toàn bộ queue
    const handleQueueUpdated = () => {
      console.log('Queue updated - refetching...');
      fetch(safeGetApiUrl('/api/message-queue?status=all'))
        .then(res => res.json())
        .then(data => {
          if (data.success) {
            console.log('Queue refetched:', data.messages.length, 'messages');
            setMessageQueue(data.messages);
          }
        })
        .catch(err => console.error('Failed to refetch queue:', err));
    };

    // Khi có message-unsent thì cập nhật queue
    const handleMessageUnsent = (data) => {
      console.log('Message unsent:', data);
      // Xóa message khỏi queue dựa vào instagram_message_id hoặc username
      setMessageQueue(prev => prev.filter(msg => {
        // Xóa nếu match instagram_message_id
        if (data.instagram_message_id && msg.instagram_message_id === data.instagram_message_id) {
          return false;
        }
        // Hoặc xóa nếu match username và status completed
        if (data.username && msg.username === data.username && msg.status === 'completed') {
          return false;
        }
        return true;
      }));
    };

    socket.on('message-status-update', handleStatusUpdate);
    socket.on('messageStatusUpdate', handleStatusUpdateAlt); // Lắng nghe cả hai tên event
    socket.on('message-queue-updated', handleQueueUpdated);
    socket.on('message-unsent', handleMessageUnsent);

    return () => {
      socket.off('message-status-update', handleStatusUpdate);
      socket.off('messageStatusUpdate', handleStatusUpdateAlt);
      socket.off('message-queue-updated', handleQueueUpdated);
      socket.off('message-unsent', handleMessageUnsent);
    };
  }, [socket]);

  const handleUnsendSuccess = () => {
    setMessageQueue(prev => prev.filter(m => {
      // Loại bỏ nếu match comment_id hoặc username+text
      if (m.comment_id === comment.id) return false;
      if (m.username === comment.username && m.original_comment === comment.text) return false;
      return true;
    }));
  };

  return (
    <div className="h-full">
      {/* Comments Stream - Full height scrollable */}
      <div
        className="h-full overflow-y-auto p-2 space-y-2 comments-container"
        style={{
          overscrollBehavior: 'contain',
          WebkitOverflowScrolling: 'touch',
          overflowAnchor: 'none'
        }}
        onScroll={handleScroll}
        onWheel={handleUserInteraction}
        onTouchStart={handleTouchStart}
        onTouchEnd={handleTouchEnd}
        onTouchMove={() => {
          // During touch move, clear timeouts and cancel hold detection
          if (interactionTimeoutRef.current) {
            clearTimeout(interactionTimeoutRef.current);
          }
          if (holdTimeoutRef.current) {
            clearTimeout(holdTimeoutRef.current);
          }
          // Cancel hold if user moves (mobile only)
          if (isMobile) {
            setIsHolding(false);
          }
        }}
      >
        {displayedComments.length > 0 && (
          <>
            {/* Loading indicator cho comments cũ hơn - đặt ở đầu danh sách */}
            {hasEnoughCommentsToLoad ? (
              <div ref={commentsLoadingRef} className="py-2 text-center sticky top-0 bg-white bg-opacity-80 z-10">
                {isLoadingMore ? (
                  <div className="flex items-center justify-center">
                    <Loader className="h-4 w-4 animate-spin text-gray-400 mr-2" />
                    <span className="text-xs text-gray-500">Đang tải bình luận cũ hơn...</span>
                  </div>
                ) : (
                  <div className="text-xs text-gray-500">Kéo lên để xem bình luận cũ hơn</div>
                )}
              </div>
            ) : (
              <div className="py-2 text-center sticky top-0 bg-white bg-opacity-80 z-10">
                <div className="text-xs text-gray-400">
                  {recentComments.length < 100 ? (
                    ``
                  ) : (
                    "Đã hiển thị tất cả bình luận"
                  )}
                </div>
              </div>
            )}

            {/* Comment list */}
            {displayedComments.map((comment, index) => {
              const isRegular = regularCustomers.has(comment.username);
              const currentColors = isRegular ? colorSettings.regularCustomer : colorSettings.newCustomer;
              const queueStatus = getQueueStatusForComment(comment);
              const queueMeta = getQueueMetaForComment(comment);
              const isProcessing = processingComments.has(comment.id);

              // Đổi màu nền theo trạng thái hàng chờ (include lastRefresh to force re-calculation)
              let highlightColor;

              // Ưu tiên trạng thái queue trước, sau đó mới đến khách quen
              if (queueStatus === 'pending') {
                highlightColor = '#dbeafe'; // xanh dương nhạt - tin nhắn đang chờ
              } else if (queueStatus === 'processing') {
                highlightColor = '#fce7f3'; // hồng nhạt - tin nhắn đang xử lý
              } else if (queueStatus === 'completed') {
                highlightColor = '#bbf7d0'; // xanh lá nhạt - tin nhắn đã hoàn thành
              } else if (queueStatus === 'unsent' || queueStatus === 'cancelled') {
                // Trở về màu mặc định hoặc màu khách quen
                highlightColor = isRegular ? '#fef3c7' : '#ffffff'; // vàng nhạt cho khách quen, trắng cho khách mới
              } else {
                // Không có trạng thái queue - hiển thị màu mặc định
                highlightColor = isRegular ? '#fef3c7' : '#ffffff'; // vàng nhạt cho khách quen, trắng cho khách mới
              }

              // Debug log for color changes
              console.log(`🎨 Comment ${comment.id} color: queue=${queueStatus || 'none'}, regular=${isRegular} → ${highlightColor} (refresh: ${lastRefresh})`);

              // Quyết định hiển thị nút thu hồi
              const unsendEnabled = (queueStatus === 'pending' || queueStatus === 'completed') && queueMeta.instagram_message_id;
              const unsendDisabled = queueStatus === 'processing';

              const handleUnsendSuccess = () => {
                setMessageQueue(prev => prev.filter(m => {
                  // Loại bỏ nếu match comment_id hoặc username+text
                  if (m.comment_id === comment.id) return false;
                  if (m.username === comment.username && m.original_comment === comment.text) return false;
                  return true;
                }));
              };

              return (
                <CommentItem
                  key={comment.id || index}
                  comment={comment}
                  isRegular={isRegular}
                  currentColors={{ ...currentColors, background: highlightColor }}
                  regularCustomers={regularCustomers}
                  toggleRegularCustomer={toggleRegularCustomer}
                  isConnected={isConnected}
                  queueStatus={queueStatus}
                  unsendEnabled={unsendEnabled}
                  unsendDisabled={unsendDisabled}
                  queueMeta={queueMeta}
                  onUnsendSuccess={handleUnsendSuccess}
                />
              );
            })}
          </>
        )}

        {displayedComments.length === 0 && (
          <div className="flex flex-col items-center justify-center h-full text-gray-500 p-6">
            {/* Check if both services are not running */}
            {(!systemState?.isRunning && !systemState?.messengerConnected) ? (
              // Show quick start interface
              <div className="w-full max-w-sm sm:max-w-md">
                {/* Check if cookies/credentials are available and services are running */}
                {(quickStartData.scraperCookies || hasSavedScraperCredentials) &&
                  ((quickStartData.useInstagrapi && (quickStartData.instagrapiConnected || hasSavedInstagrapiCredentials) && quickStartData.instagrapiRunning) ||
                    (!quickStartData.useInstagrapi && (quickStartData.messengerCookies || hasSavedMessengerCredentials) && quickStartData.messengerRunning)) &&
                  quickStartData.scraperRunning ? (
                  // Both cookies available - show quick start
                  <div className="bg-white rounded-lg shadow-lg p-3 sm:p-6 border border-gray-200">
                    <div className="text-center mb-4 sm:mb-6">
                      <Play className="h-8 w-8 sm:h-12 sm:w-12 mx-auto mb-2 sm:mb-3 text-blue-600" />
                      <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-1 sm:mb-2">Khởi động nhanh</h3>
                      <p className="text-xs sm:text-sm text-gray-600">
                        Bắt đầu thu thập bình luận và tin nhắn tự động
                      </p>
                    </div>

                    {/* Instagram Live Username Input */}
                    <div className="mb-3 sm:mb-4">
                      <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1 sm:mb-2">
                        Tên Instagram Live
                      </label>
                      <input
                        type="text"
                        value={quickStartData.liveUsername}
                        onChange={(e) => setQuickStartData(prev => ({ ...prev, liveUsername: e.target.value }))}
                        placeholder="Nhập tên Instagram Live..."
                        className="w-full px-2 py-1.5 sm:px-3 sm:py-2 text-sm sm:text-base border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>

                    {/* Action Buttons */}
                    <div className="space-y-2 sm:space-y-3">
                      <button
                        onClick={handleQuickStartScraper}
                        disabled={quickStartData.isStartingScraper || !quickStartData.liveUsername.trim()}
                        className="w-full flex items-center justify-center px-3 py-2 sm:px-4 sm:py-2 text-sm sm:text-base bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors touch-manipulation"
                      >
                        {quickStartData.isStartingScraper ? (
                          <>
                            <Loader className="h-3 w-3 sm:h-4 sm:w-4 mr-2 animate-spin" />
                            <span className="text-xs sm:text-sm">Đang khởi động...</span>
                          </>
                        ) : (
                          <>
                            <MessageCircle className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                            <span className="text-xs sm:text-sm">Thu thập bình luận</span>
                          </>
                        )}
                      </button>

                      <button
                        onClick={handleQuickStartMessenger}
                        disabled={quickStartData.isStartingMessenger}
                        className="w-full flex items-center justify-center px-3 py-2 sm:px-4 sm:py-2 text-sm sm:text-base bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors touch-manipulation"
                      >
                        {quickStartData.isStartingMessenger ? (
                          <>
                            <Loader className="h-3 w-3 sm:h-4 sm:w-4 mr-2 animate-spin" />
                            <span className="text-xs sm:text-sm">Đang khởi động...</span>
                          </>
                        ) : (
                          <>
                            <Play className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                            <span className="text-xs sm:text-sm">Tin nhắn tự động</span>
                          </>
                        )}
                      </button>
                    </div>

                    <div className="mt-4 text-xs text-gray-500 text-center">
                      Cả hai chức năng đã sẵn sàng và đang hoạt động
                    </div>
                  </div>
                ) : (
                  // Missing cookies - show login instructions
                  <div className="bg-white rounded-lg shadow-lg p-3 sm:p-6 border border-gray-200">
                    <div className="text-center mb-4 sm:mb-6">
                      {/* Check if all have saved credentials */}
                      {(hasSavedScraperCredentials &&
                        ((quickStartData.useInstagrapi && hasSavedInstagrapiCredentials) ||
                          (!quickStartData.useInstagrapi && hasSavedMessengerCredentials))) ? (
                        <>
                          <CheckCircle className="h-8 w-8 sm:h-12 sm:w-12 mx-auto mb-2 sm:mb-3 text-green-500" />
                          <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-1 sm:mb-2">Sẵn sàng khởi động</h3>
                          <p className="text-xs sm:text-sm text-gray-600">
                            Tất cả chức năng đã có thông tin đăng nhập được lưu
                          </p>
                        </>
                      ) : (
                        <>
                          <AlertCircle className="h-8 w-8 sm:h-12 sm:w-12 mx-auto mb-2 sm:mb-3 text-orange-500" />
                          <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-1 sm:mb-2">Cần đăng nhập</h3>
                          <p className="text-xs sm:text-sm text-gray-600">
                            Vui lòng đăng nhập vào các chức năng sau để sử dụng
                          </p>
                        </>
                      )}
                    </div>

                    <div className="space-y-2 sm:space-y-3">
                      {!quickStartData.scraperCookies && !hasSavedScraperCredentials && (
                        <div className={`flex flex-col sm:flex-row sm:items-center sm:justify-between p-2 sm:p-3 rounded-lg space-y-2 sm:space-y-0 ${hasSavedScraperCredentials
                          ? 'bg-green-50 border border-green-200'
                          : 'bg-red-50 border border-red-200'
                          }`}>
                          <div className="flex items-center">
                            {hasSavedScraperCredentials ? (
                              <CheckCircle className="h-4 w-4 sm:h-5 sm:w-5 text-green-500 mr-2" />
                            ) : (
                              <AlertCircle className="h-4 w-4 sm:h-5 sm:w-5 text-red-500 mr-2" />
                            )}
                            <span className={`text-xs sm:text-sm ${hasSavedScraperCredentials ? 'text-green-700' : 'text-red-700'}`}>
                              {hasSavedScraperCredentials ? 'Thu thập bình luận - Sẵn sàng' : 'Thu thập bình luận - Cần đăng nhập'}
                            </span>
                          </div>
                          <button
                            onClick={() => setShowScraperLoginPopup(true)}
                            className="flex items-center justify-center px-2 py-1 sm:px-3 sm:py-1 bg-red-600 text-white text-xs sm:text-sm rounded hover:bg-red-700 transition-colors touch-manipulation w-full sm:w-auto"
                          >
                            <LogIn className="h-3 w-3 mr-1" />
                            {hasSavedScraperCredentials ? 'Bắt đầu' : 'Đăng nhập'}
                          </button>
                        </div>
                      )}

                      {quickStartData.scraperCookies && !quickStartData.scraperRunning && (
                        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 sm:p-4">
                          <div className="flex items-center mb-3">
                            <AlertCircle className="h-4 w-4 sm:h-5 sm:w-5 text-yellow-500 mr-2" />
                            <span className="text-xs sm:text-sm text-yellow-700 font-medium">Thu thập bình luận - Chưa khởi động</span>
                          </div>

                          {/* Instagram Live Username Input */}
                          <div className="mb-3">
                            <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
                              Tên Instagram Live
                            </label>
                            <input
                              type="text"
                              value={quickStartData.liveUsername}
                              onChange={(e) => setQuickStartData(prev => ({ ...prev, liveUsername: e.target.value }))}
                              placeholder="Nhập tên Instagram Live..."
                              className="w-full px-2 py-1.5 sm:px-3 sm:py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                              disabled={quickStartData.isStartingScraper}
                            />
                            <p className="text-xs text-gray-500 mt-1">
                              Ví dụ: nếu URL là instagram.com/username/live thì nhập "username"
                            </p>
                          </div>

                          <button
                            onClick={handleQuickStartScraper}
                            disabled={quickStartData.isStartingScraper || !quickStartData.liveUsername.trim()}
                            className="w-full flex items-center justify-center px-3 py-2 text-sm bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                          >
                            {quickStartData.isStartingScraper ? (
                              <>
                                <Loader className="h-4 w-4 mr-2 animate-spin" />
                                Đang khởi động...
                              </>
                            ) : (
                              <>
                                <Play className="h-4 w-4 mr-2" />
                                Khởi động thu thập bình luận
                              </>
                            )}
                          </button>
                        </div>
                      )}

                      {/* Messenger status - different for Chrome Testing vs Instagrapi */}
                      {quickStartData.useInstagrapi ? (
                        // Instagrapi mode
                        !quickStartData.instagrapiConnected && (
                          <div className={`flex flex-col sm:flex-row sm:items-center sm:justify-between p-2 sm:p-3 rounded-lg space-y-2 sm:space-y-0 ${hasSavedInstagrapiCredentials
                            ? 'bg-green-50 border border-green-200'
                            : 'bg-red-50 border border-red-200'
                            }`}>
                            <div className="flex items-center">
                              {hasSavedInstagrapiCredentials ? (
                                <CheckCircle className="h-4 w-4 sm:h-5 sm:w-5 text-green-500 mr-2" />
                              ) : (
                                <AlertCircle className="h-4 w-4 sm:h-5 sm:w-5 text-red-500 mr-2" />
                              )}
                              <span className={`text-xs sm:text-sm ${hasSavedInstagrapiCredentials ? 'text-green-700' : 'text-red-700'}`}>
                                {hasSavedInstagrapiCredentials ? 'Instagrapi API - Sẵn sàng' : 'Instagrapi API - Cần đăng nhập'}
                              </span>
                            </div>
                            <button
                              onClick={hasSavedInstagrapiCredentials ? handleQuickStartInstagrapi : () => setShowMessengerLoginPopup(true)}
                              disabled={quickStartData.isStartingMessenger}
                              className={`flex items-center justify-center px-2 py-1 sm:px-3 sm:py-1 text-white text-xs sm:text-sm rounded transition-colors touch-manipulation w-full sm:w-auto ${hasSavedInstagrapiCredentials
                                ? 'bg-green-600 hover:bg-green-700'
                                : 'bg-red-600 hover:bg-red-700'
                                } ${quickStartData.isStartingMessenger ? 'opacity-50 cursor-not-allowed' : ''}`}
                            >
                              {quickStartData.isStartingMessenger ? (
                                <>
                                  <Loader className="h-3 w-3 mr-1 animate-spin" />
                                  Đang khởi động...
                                </>
                              ) : (
                                <>
                                  <LogIn className="h-3 w-3 mr-1" />
                                  {hasSavedInstagrapiCredentials ? 'Bắt đầu' : 'Đăng nhập'}
                                </>
                              )}
                            </button>
                          </div>
                        )
                      ) : (
                        // Chrome Testing mode
                        !quickStartData.messengerCookies && !hasSavedMessengerCredentials && (
                          <div className={`flex flex-col sm:flex-row sm:items-center sm:justify-between p-2 sm:p-3 rounded-lg space-y-2 sm:space-y-0 ${hasSavedMessengerCredentials
                            ? 'bg-green-50 border border-green-200'
                            : 'bg-red-50 border border-red-200'
                            }`}>
                            <div className="flex items-center">
                              {hasSavedMessengerCredentials ? (
                                <CheckCircle className="h-4 w-4 sm:h-5 sm:w-5 text-green-500 mr-2" />
                              ) : (
                                <AlertCircle className="h-4 w-4 sm:h-5 sm:w-5 text-red-500 mr-2" />
                              )}
                              <span className={`text-xs sm:text-sm ${hasSavedMessengerCredentials ? 'text-green-700' : 'text-red-700'}`}>
                                {hasSavedMessengerCredentials ? 'Chrome Testing - Sẵn sàng' : 'Chrome Testing - Cần đăng nhập'}
                              </span>
                            </div>
                            <button
                              onClick={() => setShowMessengerLoginPopup(true)}
                              className="flex items-center justify-center px-2 py-1 sm:px-3 sm:py-1 bg-red-600 text-white text-xs sm:text-sm rounded hover:bg-red-700 transition-colors touch-manipulation w-full sm:w-auto"
                            >
                              <LogIn className="h-3 w-3 mr-1" />
                              {hasSavedMessengerCredentials ? 'Bắt đầu' : 'Đăng nhập'}
                            </button>
                          </div>
                        )
                      )}

                      {/* Ready to start status - different for Chrome Testing vs Instagrapi */}
                      {quickStartData.useInstagrapi ? (
                        // Instagrapi mode - ready to start
                        quickStartData.instagrapiConnected && !quickStartData.instagrapiRunning && (
                          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 sm:p-4">
                            <div className="flex items-center mb-3">
                              <AlertCircle className="h-4 w-4 sm:h-5 sm:w-5 text-yellow-500 mr-2" />
                              <span className="text-xs sm:text-sm text-yellow-700 font-medium">Instagrapi API - Chưa khởi động</span>
                            </div>

                            <button
                              onClick={handleQuickStartMessenger}
                              disabled={quickStartData.isStartingMessenger}
                              className="w-full flex items-center justify-center px-3 py-2 text-sm bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                            >
                              {quickStartData.isStartingMessenger ? (
                                <>
                                  <Loader className="h-4 w-4 mr-2 animate-spin" />
                                  Đang khởi động...
                                </>
                              ) : (
                                <>
                                  <Play className="h-4 w-4 mr-2" />
                                  Khởi động Instagrapi
                                </>
                              )}
                            </button>
                          </div>
                        )
                      ) : (
                        // Chrome Testing mode - ready to start
                        quickStartData.messengerCookies && !quickStartData.messengerRunning && (
                          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 sm:p-4">
                            <div className="flex items-center mb-3">
                              <AlertCircle className="h-4 w-4 sm:h-5 sm:w-5 text-yellow-500 mr-2" />
                              <span className="text-xs sm:text-sm text-yellow-700 font-medium">Chrome Testing - Chưa khởi động</span>
                            </div>

                            <button
                              onClick={handleQuickStartMessenger}
                              disabled={quickStartData.isStartingMessenger}
                              className="w-full flex items-center justify-center px-3 py-2 text-sm bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                            >
                              {quickStartData.isStartingMessenger ? (
                                <>
                                  <Loader className="h-4 w-4 mr-2 animate-spin" />
                                  Đang khởi động...
                                </>
                              ) : (
                                <>
                                  <Play className="h-4 w-4 mr-2" />
                                  Khởi động Chrome Testing
                                </>
                              )}
                            </button>
                          </div>
                        )
                      )}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              // At least one service is running - show normal empty state
              <div className="text-center">
                <MessageCircle className="h-16 w-16 mb-4 text-gray-400 mx-auto" />
                <h3 className="text-lg font-medium mb-2 text-gray-600">Chưa có bình luận nào</h3>
                <p className="text-sm text-center text-gray-500">
                  Bình luận sẽ xuất hiện ở đây khi có người bình luận trên Instagram Live
                </p>
              </div>
            )}
          </div>
        )}

        {/* Auto scroll anchor */}
        <div ref={commentsEndRef} />
      </div>

      {/* Login Popups */}
      <LoginPopup
        isOpen={showScraperLoginPopup}
        onClose={() => setShowScraperLoginPopup(false)}
        onLogin={handleScraperPopupLogin}
        title={quickStartData.scraperCookies && !quickStartData.scraperRunning
          ? "Khởi động thu thập bình luận"
          : "Đăng nhập Instagram - Thu thập bình luận"
        }
        isLoading={quickStartData.isStartingScraper}
        showLiveUsernameInput={true}
        liveUsername={quickStartData.liveUsername}
        onLiveUsernameChange={(value) => setQuickStartData(prev => ({ ...prev, liveUsername: value }))}
        startupMode={quickStartData.scraperCookies && !quickStartData.scraperRunning}
        credentialsType="scraper"
      />

      <LoginPopup
        isOpen={showMessengerLoginPopup}
        onClose={() => setShowMessengerLoginPopup(false)}
        onLogin={handleMessengerPopupLogin}
        title={quickStartData.useInstagrapi
          ? (quickStartData.instagrapiConnected && !quickStartData.instagrapiRunning
            ? "Khởi động Instagrapi API"
            : "Đăng nhập Instagrapi API")
          : (quickStartData.messengerCookies && !quickStartData.messengerRunning
            ? "Khởi động Chrome Testing"
            : "Đăng nhập Instagram - Chrome Testing")
        }
        isLoading={quickStartData.isStartingMessenger}
        startupMode={quickStartData.messengerCookies && !quickStartData.messengerRunning}
        credentialsType={quickStartData.useInstagrapi ? "instagrapi" : "messenger"}
      />
    </div>
  );
};

export default Comments;

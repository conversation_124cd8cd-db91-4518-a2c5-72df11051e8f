import React from 'react';
import { NavLink } from 'react-router-dom';
import {
  History,
  MessageCircle,
  MessageSquare,
  Settings
} from 'lucide-react';
import { useSocket } from '../contexts/SocketContext';

const BottomNavigation = () => {
  const { systemState } = useSocket();

  const navigation = [
    {
      name: '<PERSON><PERSON><PERSON> luận',
      href: '/comments',
      icon: MessageCircle,
      badge: systemState.totalComments > 0 ? systemState.totalComments : null
    },
    {
      name: 'Tin nhắn',
      href: '/auto-messages',
      icon: MessageSquare,
      badge: null
    },
    {
      name: '<PERSON><PERSON><PERSON> sử',
      href: '/history',
      icon: History,
      badge: null
    },
    {
      name: '<PERSON>à<PERSON> đặt',
      href: '/settings',
      icon: Settings,
      badge: null
    }
  ];

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 safe-area-bottom z-30">
      <nav className="flex justify-around">
        {navigation.map((item) => {
          const Icon = item.icon;

          return (
            <NavLink
              key={item.name}
              to={item.href}
              className={({ isActive }) =>
                `relative flex flex-col items-center justify-center py-2 px-3 min-w-0 flex-1 text-center transition-colors duration-200 ${isActive
                  ? 'text-purple-600'
                  : 'text-gray-500 hover:text-gray-700'
                }`
              }
            >
              {({ isActive }) => (
                <>
                  <div className="relative">
                    <Icon className={`h-6 w-6 ${isActive ? 'text-purple-600' : 'text-gray-500'}`} />

                    {item.badge && (
                      <span className="absolute -top-2 -right-2 inline-flex items-center justify-center px-1.5 py-0.5 text-xs font-bold leading-none text-white bg-red-500 rounded-full min-w-[18px] h-[18px]">
                        {item.badge > 99 ? '99+' : item.badge}
                      </span>
                    )}
                  </div>

                  <span className={`mt-1 text-xs font-medium truncate w-full ${isActive ? 'text-purple-600' : 'text-gray-500'
                    }`}>
                    {item.name}
                  </span>

                  {isActive && (
                    <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-8 h-0.5 bg-purple-600 rounded-full"></div>
                  )}
                </>
              )}
            </NavLink>
          );
        })}
      </nav>
    </div>
  );
};

export default BottomNavigation;

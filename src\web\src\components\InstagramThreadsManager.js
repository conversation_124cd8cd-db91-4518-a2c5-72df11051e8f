import React, { useState, useEffect } from 'react';
import { MessageCircle, Trash2, Refresh<PERSON><PERSON>, Clock, User } from 'lucide-react';
import { getApiUrl } from '../config/api';
import toast from 'react-hot-toast';

const InstagramThreadsManager = () => {
  const [threads, setThreads] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [stats, setStats] = useState({ total: 0 });

  useEffect(() => {
    loadThreads();
  }, []);

  const loadThreads = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(getApiUrl('/api/instagram-threads'));
      const data = await response.json();
      if (data.success) {
        setThreads(data.threads);
        setStats({ total: data.total });
      }
    } catch (error) {
      console.error('Failed to load Instagram threads:', error);
      toast.error('Lỗi khi tải danh sách thread cache');
    } finally {
      setIsLoading(false);
    }
  };

  const deleteThread = async (username) => {
    if (!confirm(`Xóa thread cache cho @${username}?`)) return;

    try {
      const response = await fetch(`/api/instagram-threads/${username}`, {
        method: 'DELETE'
      });
      const data = await response.json();

      if (data.success) {
        toast.success(`Đã xóa thread cache cho @${username}`);
        loadThreads();
      } else {
        toast.error(data.error || 'Lỗi khi xóa thread cache');
      }
    } catch (error) {
      console.error('Failed to delete thread:', error);
      toast.error('Lỗi khi xóa thread cache');
    }
  };

  const cleanupOldThreads = async () => {
    if (!confirm('Xóa tất cả thread cache cũ hơn 30 ngày?')) return;

    try {
      const response = await fetch('/api/instagram-threads/cleanup', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ olderThanDays: 30 })
      });
      const data = await response.json();

      if (data.success) {
        toast.success(`Đã dọn dẹp ${data.deletedCount} thread cache cũ`);
        loadThreads();
      } else {
        toast.error('Lỗi khi dọn dẹp thread cache');
      }
    } catch (error) {
      console.error('Failed to cleanup threads:', error);
      toast.error('Lỗi khi dọn dẹp thread cache');
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('vi-VN');
  };

  const getTimeSinceLastUsed = (lastUsed) => {
    const now = new Date();
    const lastUsedDate = new Date(lastUsed);
    const diffMs = now - lastUsedDate;
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

    if (diffDays > 0) {
      return `${diffDays} ngày trước`;
    } else if (diffHours > 0) {
      return `${diffHours} giờ trước`;
    } else {
      return 'Vừa xong';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <MessageCircle className="h-6 w-6 text-blue-600" />
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Instagram Thread Cache</h2>
            <p className="text-sm text-gray-600">
              Quản lý cache thread ID để tăng tốc độ gửi tin nhắn
            </p>
          </div>
        </div>

        <div className="flex space-x-2">
          <button
            onClick={loadThreads}
            disabled={isLoading}
            className="btn-secondary"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Làm mới
          </button>

          <button
            onClick={cleanupOldThreads}
            className="btn-secondary text-orange-600 hover:text-orange-700"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Dọn dẹp cũ
          </button>
        </div>
      </div>

      {/* Stats */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <MessageCircle className="h-5 w-5 text-blue-600" />
            <span className="text-sm font-medium text-blue-900">
              Tổng số thread cache: {stats.total}
            </span>
          </div>
        </div>
      </div>

      {/* Threads List */}
      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Danh sách Thread Cache</h3>
        </div>

        {isLoading ? (
          <div className="p-8 text-center">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto text-gray-400 mb-4" />
            <p className="text-gray-500">Đang tải...</p>
          </div>
        ) : threads.length === 0 ? (
          <div className="p-8 text-center">
            <MessageCircle className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <p className="text-gray-500">Chưa có thread cache nào</p>
            <p className="text-sm text-gray-400 mt-2">
              Thread cache sẽ được tạo tự động khi gửi tin nhắn lần đầu
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {threads.map((thread) => (
              <div key={thread.id} className="p-4 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <User className="h-5 w-5 text-gray-400" />
                    <div>
                      <div className="font-medium text-gray-900">@{thread.username}</div>
                      <div className="text-sm text-gray-500">
                        Thread ID: {thread.thread_id}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      <div className="flex items-center text-sm text-gray-500">
                        <Clock className="h-4 w-4 mr-1" />
                        {getTimeSinceLastUsed(thread.last_used)}
                      </div>
                      <div className="text-xs text-gray-400">
                        {formatDate(thread.last_used)}
                      </div>
                    </div>

                    <button
                      onClick={() => deleteThread(thread.username)}
                      className="p-2 text-red-600 hover:text-red-700 hover:bg-red-50 rounded"
                      title="Xóa thread cache"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Info */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-2">Thông tin về Thread Cache</h4>
        <ul className="text-sm text-gray-600 space-y-1">
          <li>• Thread cache giúp tăng tốc độ gửi tin nhắn cho các user đã nhắn tin trước đó</li>
          <li>• Lần đầu gửi tin: Profile → 3 chấm → Send message (chậm)</li>
          <li>• Lần sau gửi tin: Truy cập trực tiếp thread ID (nhanh)</li>
          <li>• Cache tự động được tạo và cập nhật khi gửi tin nhắn</li>
          <li>• Nên dọn dẹp cache cũ định kỳ để tối ưu hiệu suất</li>
        </ul>
      </div>
    </div>
  );
};

export default InstagramThreadsManager;

import { useState, useEffect } from 'react';
import credentialsService from '../services/credentialsService';

// Hook để quản lý tự động đăng nhập khi khởi động app
export const useAutoLogin = (credentialsType, loginFunction) => {
  const [isAutoLogging, setIsAutoLogging] = useState(false);
  const [autoLoginAttempted, setAutoLoginAttempted] = useState(false);

  useEffect(() => {
    const attemptAutoLogin = async () => {
      if (autoLoginAttempted || !loginFunction) return;
      
      setAutoLoginAttempted(true);
      
      const savedCredentials = credentialsService.getSavedCredentials(credentialsType);
      if (savedCredentials) {
        setIsAutoLogging(true);
        try {
          await loginFunction(savedCredentials.username, savedCredentials.password, true);
        } catch (error) {
          console.error('Auto login failed:', error);
        } finally {
          setIsAutoLogging(false);
        }
      }
    };

    attemptAutoLogin();
  }, [credentialsType, loginFunction, autoLoginAttempted]);

  const resetAutoLogin = () => {
    setAutoLoginAttempted(false);
  };

  return {
    isAutoLogging,
    autoLoginAttempted,
    resetAutoLogin
  };
};

export default useAutoLogin;

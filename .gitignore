# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build
dist/
# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Database
src/backend/data/
*.db
*.sqlite
*.sqlite3

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Electron
dist/
build/
out/

# React build
src/web/build/
src/web/dist/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Temporary files
tmp/
temp/

# Redis dump
dump.rdb

# Screenshots and recordings
screenshots/
recordings/

# Puppeteer cache
.cache/puppeteer/

# Local configuration
config/local.json

# Test files
test-results/
playwright-report/

# Backup files
*.bak
*.backup

# Lock files (keep yarn.lock but ignore package-lock.json for consistency)
package-lock.json

# Build artifacts
*.tsbuildinfo

# Local development
.local/

# Certificates
*.pem
*.key
*.crt

# Docker
.dockerignore
Dockerfile
docker-compose.yml

# Kubernetes
k8s/

# Terraform
*.tfstate
*.tfstate.*
.terraform/

# AWS
.aws/

# Google Cloud
.gcloud/

# Azure
.azure/

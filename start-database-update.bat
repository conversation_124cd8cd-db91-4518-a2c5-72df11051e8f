@echo off
echo ================================================
echo Updating database for Instagrapi integration...
echo ================================================

cd /d "%~dp0"

echo Creating SQLite script...
echo BEGIN TRANSACTION; > update_database.sql
echo -- Add instagram_message_id column to message_queue table if it doesn't exist >> update_database.sql
echo CREATE TABLE IF NOT EXISTS temp_table AS SELECT * FROM message_queue; >> update_database.sql
echo DROP TABLE message_queue; >> update_database.sql
echo CREATE TABLE message_queue ( >> update_database.sql
echo   id TEXT PRIMARY KEY, >> update_database.sql
echo   comment_id TEXT, >> update_database.sql
echo   username TEXT NOT NULL, >> update_database.sql
echo   original_comment TEXT NOT NULL, >> update_database.sql
echo   customer_type TEXT NOT NULL, >> update_database.sql
echo   template_name TEXT, >> update_database.sql
echo   template_type TEXT DEFAULT 'normal', >> update_database.sql
echo   status TEXT DEFAULT 'pending', >> update_database.sql
echo   retries INTEGER DEFAULT 0, >> update_database.sql
echo   max_retries INTEGER DEFAULT 3, >> update_database.sql
echo   error_message TEXT, >> update_database.sql
echo   instagram_message_id TEXT, >> update_database.sql
echo   thread_id TEXT, >> update_database.sql
echo   created_at DATETIME DEFAULT CURRENT_TIMESTAMP, >> update_database.sql
echo   updated_at DATETIME DEFAULT CURRENT_TIMESTAMP, >> update_database.sql
echo   scheduled_at DATETIME, >> update_database.sql
echo   processed_at DATETIME >> update_database.sql
echo ); >> update_database.sql
echo INSERT INTO message_queue SELECT id, comment_id, username, original_comment, customer_type, template_name, template_type, status, retries, max_retries, error_message, NULL, NULL, created_at, updated_at, scheduled_at, processed_at FROM temp_table; >> update_database.sql
echo DROP TABLE temp_table; >> update_database.sql
echo COMMIT; >> update_database.sql

echo Running SQLite script...
sqlite3 src/backend/data/instagram_live.db < update_database.sql

echo Cleaning up...
del update_database.sql

echo Database update completed!
echo.
echo Press any key to exit...
pause > nul 
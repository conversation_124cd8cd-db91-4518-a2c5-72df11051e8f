@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Custom CSS for Instagram Live Comment System */

/* Base styles */
@layer base {
  html {
    scroll-behavior: smooth;
    height: 100%;
  }

  body {
    @apply antialiased;
    font-feature-settings: 'cv03', 'cv04', 'cv11';
    height: 100%;
  }

  #root {
    height: 100vh;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }

  /* Firefox scrollbar */
  * {
    scrollbar-width: thin;
    scrollbar-color: #d1d5db #f3f4f6;
  }
}

/* Component styles */
@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200;
  }

  .btn-primary {
    @apply btn bg-sky-500 text-white hover:bg-sky-600 focus:ring-sky-500;
  }

  .btn-secondary {
    @apply btn bg-gray-100 text-gray-700 hover:bg-gray-200 focus:ring-gray-500;
  }

  .btn-success {
    @apply btn bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
  }

  .btn-danger {
    @apply btn bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
  }

  .btn-warning {
    @apply btn bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500;
  }

  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }

  .btn-lg {
    @apply px-6 py-3 text-base;
  }

  .input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-transparent transition-all duration-200;
  }

  .textarea {
    @apply input resize-none;
  }

  .select {
    @apply input pr-10 bg-white;
  }

  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden;
  }

  .card-header {
    @apply px-6 py-4 border-b border-gray-200 bg-gray-50;
  }

  .card-body {
    @apply px-6 py-4;
  }

  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
  }

  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply badge bg-sky-100 text-sky-800;
  }

  .badge-success {
    @apply badge bg-green-100 text-green-800;
  }

  .badge-warning {
    @apply badge bg-yellow-100 text-yellow-800;
  }

  .badge-danger {
    @apply badge bg-red-100 text-red-800;
  }

  .badge-info {
    @apply badge bg-blue-100 text-blue-800;
  }

  .alert {
    @apply p-4 rounded-lg border;
  }

  .alert-success {
    @apply alert bg-green-50 border-green-200 text-green-800;
  }

  .alert-warning {
    @apply alert bg-yellow-50 border-yellow-200 text-yellow-800;
  }

  .alert-danger {
    @apply alert bg-red-50 border-red-200 text-red-800;
  }

  .alert-info {
    @apply alert bg-blue-50 border-blue-200 text-blue-800;
  }

  .spinner {
    @apply inline-block w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin;
  }

  .spinner-lg {
    @apply w-8 h-8 border-4;
  }

  .loading-overlay {
    @apply absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50;
  }

  .comment-card {
    @apply card hover:shadow-md transition-shadow duration-200;
  }

  .comment-card.new {
    @apply border-sky-300 bg-sky-50;
    animation: pulse 2s ease-in-out;
  }

  .comment-card.processed {
    @apply border-green-300 bg-green-50;
  }

  .metric-card {
    @apply card p-6 text-center bg-gradient-to-br from-sky-500 to-sky-600 text-white;
  }

  .metric-value {
    @apply text-3xl font-bold mb-2;
  }

  .metric-label {
    @apply text-sm opacity-90;
  }

  .status-indicator {
    @apply w-3 h-3 rounded-full inline-block;
  }

  .status-online {
    @apply bg-green-500;
  }

  .status-offline {
    @apply bg-red-500;
  }

  .status-warning {
    @apply bg-yellow-500;
  }

  .nav-link {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200;
  }

  .nav-link.active {
    @apply bg-sky-100 text-sky-900 border-r-2 border-sky-500;
  }

  .nav-link:not(.active) {
    @apply text-gray-600 hover:bg-gray-50 hover:text-gray-900;
  }
}

/* Utility styles */
@layer utilities {
  .text-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .bg-gradient-instagram {
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  }

  .shadow-instagram {
    box-shadow: 0 4px 14px 0 rgba(0, 118, 255, 0.39);
  }

  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-bounce-in {
    animation: bounceIn 0.5s ease-out;
  }

  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-area-left {
    padding-left: env(safe-area-inset-left);
  }

  .safe-area-right {
    padding-right: env(safe-area-inset-right);
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }

  50% {
    transform: scale(1.05);
  }

  70% {
    transform: scale(0.9);
  }

  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.8;
  }
}

/* Mobile optimizations */
@media (max-width: 640px) {
  .card {
    @apply rounded-lg;
  }

  .card-body {
    @apply px-4 py-3;
  }

  .btn {
    @apply min-h-[44px] min-w-[44px];
  }

  .metric-value {
    @apply text-2xl;
  }
}

/* Touch optimization for mobile */
.touch-manipulation {
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Better touch targets for mobile */
@media (max-width: 768px) {

  button,
  .btn,
  [role="button"] {
    min-height: 44px;
    min-width: 44px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  /* Larger text for better readability on mobile */
  .comment-text {
    font-size: 16px;
    line-height: 1.5;
  }

  /* Better spacing for mobile comments */
  .comment-item {
    padding: 16px;
    margin-bottom: 8px;
  }

  /* Larger avatars for mobile */
  .comment-avatar {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }

  /* Better button spacing on mobile */
  .comment-actions {
    gap: 12px;
    margin-top: 12px;
  }

  /* Larger dropdown for mobile */
  .mobile-dropdown {
    min-width: 280px;
    max-width: 90vw;
  }

  /* Prevent body scroll when scrolling comments */
  .comments-container {
    overscroll-behavior: contain;
    -webkit-overflow-scrolling: touch;
    overflow-anchor: none;
  }

  /* Enhanced mobile comment buttons */
  .comment-action-button {
    min-width: 50px;
    min-height: 48px;
    padding: 12px 16px;
  }

  /* Better star button alignment */
  .star-button-container {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 48px;
    min-height: 48px;
  }
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
  /* Dark mode styles can be added here */
}

/* React Select Styles */
.react-select-container .react-select__control {
  @apply border border-gray-300 rounded-md shadow-sm;
  min-height: 38px;
}

.react-select-container .react-select__control:hover {
  @apply border-gray-400;
}

.react-select-container .react-select__control--is-focused {
  @apply border-blue-500 ring-1 ring-blue-500;
  box-shadow: 0 0 0 1px rgb(59 130 246);
}

.react-select-container .react-select__value-container {
  @apply px-3 py-1;
}

.react-select-container .react-select__single-value {
  @apply text-gray-900;
}

.react-select-container .react-select__placeholder {
  @apply text-gray-500;
}

.react-select-container .react-select__input-container {
  @apply text-gray-900;
}

.react-select-container .react-select__menu {
  @apply border border-gray-300 rounded-md shadow-lg bg-white;
}

.react-select-container .react-select__menu-list {
  @apply py-1;
}

.react-select-container .react-select__option {
  @apply px-3 py-2 text-gray-900 cursor-pointer;
}

.react-select-container .react-select__option--is-focused {
  @apply bg-blue-50 text-blue-900;
}

.react-select-container .react-select__option--is-selected {
  @apply bg-blue-600 text-white;
}

.react-select-container .react-select__option--is-disabled {
  @apply text-gray-400 cursor-not-allowed bg-gray-50;
}

.react-select-container .react-select__indicator-separator {
  @apply bg-gray-300;
}

.react-select-container .react-select__dropdown-indicator {
  @apply text-gray-400;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .card {
    @apply border-2 border-gray-800;
  }

  .btn-primary {
    @apply border-2 border-sky-800;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  .print-only {
    display: block !important;
  }

  .card {
    @apply shadow-none border border-gray-400;
  }
}
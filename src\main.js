const { app, BrowserWindow, Menu, ipc<PERSON>ain, dialog, shell } = require('electron');
const path = require('path');
const os = require('os');
const Store = require('electron-store');
const notifier = require('node-notifier');
const { spawn } = require('child_process');
const QRCode = require('qrcode');

// Initialize electron store
const store = new Store();

let mainWindow;
let serverProcess;
let webProcess;
let localIP = null;

// Create main window
function createMainWindow() {
  mainWindow = new BrowserWindow({
    width: 800,
    height: 600,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true
    },
    icon: path.join(__dirname, 'assets/icon.png'),
    show: false,
    resizable: false
  });

  // Load the main HTML file
  mainWindow.loadFile(path.join(__dirname, 'renderer/index.html'));

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();

    // Auto-start server if configured
    if (store.get('autoStartServer', true)) { // Default to true
      startBackendServer();

      // Only start dev web server in development mode
      if (process.env.NODE_ENV !== 'production') {
        startWebServer();
      }

      // Auto-start web browser if configured
      if (store.get('autoStartWeb', true)) { // Default to true
        setTimeout(() => {
          openWebInterface();
        }, 3000); // Wait 3 seconds for server to start
      }
    }
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
    if (serverProcess) {
      serverProcess.kill();
    }
    if (webProcess) {
      webProcess.kill();
    }
  });

  // Open external links in browser
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });
}

// Get local IP address - prioritize 192.168.x.x addresses
function getLocalIP() {
  const networkInterfaces = os.networkInterfaces();
  const ipAddresses = [];

  // Collect all non-internal IPv4 addresses
  for (const interfaceName of Object.keys(networkInterfaces)) {
    const interfaces = networkInterfaces[interfaceName];
    for (const interface of interfaces) {
      if (interface.family === 'IPv4' && !interface.internal) {
        ipAddresses.push(interface.address);
      }
    }
  }

  // Prioritize 192.168.x.x addresses (local network)
  const localNetworkIP = ipAddresses.find(ip => ip.startsWith('192.168.'));
  if (localNetworkIP) {
    return localNetworkIP;
  }

  // Fallback to 10.x.x.x addresses (another common local network range)
  const localNetworkIP2 = ipAddresses.find(ip => ip.startsWith('10.'));
  if (localNetworkIP2) {
    return localNetworkIP2;
  }

  // Fallback to 172.16-31.x.x addresses (private network range)
  const localNetworkIP3 = ipAddresses.find(ip => {
    const parts = ip.split('.');
    return parts[0] === '172' && parseInt(parts[1]) >= 16 && parseInt(parts[1]) <= 31;
  });
  if (localNetworkIP3) {
    return localNetworkIP3;
  }

  // If no local network IP found, return the first available IP
  return ipAddresses.length > 0 ? ipAddresses[0] : 'localhost';
}

// Open web interface
function openWebInterface() {
  // In production, use production web. In development, try dev server first
  const prodUrl = 'http://localhost:3001/web';
  const devUrl = 'http://localhost:3002';

  if (process.env.NODE_ENV === 'production') {
    // Production mode - only use production URL
    shell.openExternal(prodUrl);
  } else {
    // Development mode - try dev server first, fallback to production
    const http = require('http');

    const req = http.get(devUrl, (res) => {
      shell.openExternal(devUrl);
    });

    req.on('error', () => {
      shell.openExternal(prodUrl);
    });

    req.setTimeout(2000, () => {
      req.destroy();
      shell.openExternal(prodUrl);
    });
  }
}

// Start web development server
function startWebServer() {
  try {
    const isDev = process.env.NODE_ENV === 'development';

    if (isDev) {
      // Development mode: run npm dev server
      const env = { ...process.env, PORT: '3002' };

      webProcess = spawn('npm', ['run', 'web'], {
        stdio: 'pipe',
        cwd: __dirname,
        shell: true,
        env: env
      });

      webProcess.stdout.on('data', (data) => {
        const output = data.toString();
        console.log(`Web: ${output}`);
        mainWindow?.webContents.send('web-log', output);

        // Parse Vite output to get network URLs
        if (output.includes('Network:') && output.includes('3002')) {
          const lines = output.split('\n');
          const networkLines = lines.filter(line => line.includes('Network:') && line.includes('3002'));
          if (networkLines.length > 0) {
            // Get the first network URL (usually the main IP)
            const networkLine = networkLines[0];
            // More flexible regex to handle special characters
            const urlMatch = networkLine.match(/http:\/\/([0-9.]+):3002/);
            if (urlMatch) {
              localIP = urlMatch[1];
              console.log(`Detected local IP: ${localIP}`);
              console.log(`Sending web-started event with localIP: ${localIP}`);
              mainWindow?.webContents.send('web-started', { localIP });
            }
          }
        }
      });

      webProcess.stderr.on('data', (data) => {
        console.error(`Web Error: ${data}`);
        mainWindow?.webContents.send('web-error', data.toString());
      });

      webProcess.on('close', (code) => {
        console.log(`Web process exited with code ${code}`);
        mainWindow?.webContents.send('web-stopped', code);
      });
    } else {
      // Production mode: serve static files from build directory
      const express = require('express');

      // In production, web build is in resources/web (from extraResources)
      // In development, it's in src/web/build
      let webBuildPath;
      if (app.isPackaged) {
        webBuildPath = path.join(process.resourcesPath, 'web');
      } else {
        webBuildPath = path.join(__dirname, 'web', 'build');
      }

      console.log(`Looking for web build at: ${webBuildPath}`);

      // Check if build directory exists
      if (!require('fs').existsSync(webBuildPath)) {
        console.error('Web build directory not found:', webBuildPath);
        mainWindow?.webContents.send('web-error', 'Web build directory not found. Please run build first.');
        return;
      }

      const expressApp = express();

      // Serve static files from build directory
      expressApp.use(express.static(webBuildPath));

      // Handle React Router - serve index.html for all routes
      expressApp.get('*', (req, res) => {
        res.sendFile(path.join(webBuildPath, 'index.html'));
      });

      const server = expressApp.listen(3002, () => {
        console.log('Web server running on port 3002 (production mode)');
        mainWindow?.webContents.send('web-log', 'Web server started on port 3002 (production mode)');

        // Get local IP and send web-started event
        localIP = getLocalIP();
        if (localIP) {
          console.log(`Sending web-started event with localIP: ${localIP}`);
          mainWindow?.webContents.send('web-started', { localIP });
        }
      });

      server.on('error', (error) => {
        console.error('Failed to start web server:', error);
        mainWindow?.webContents.send('web-error', `Failed to start web server: ${error.message}`);
      });

      // Store server reference for cleanup
      webProcess = {
        server,
        kill: () => {
          server.close();
          console.log('Web server stopped');
          mainWindow?.webContents.send('web-stopped', 0);
        }
      };
    }

  } catch (error) {
    console.error('Failed to start web server:', error);
    mainWindow?.webContents.send('web-error', error.message);
  }
}

// Start backend server
function startBackendServer() {
  try {
    // Ensure no existing process
    if (serverProcess) {
      console.log('Warning: Server process already exists, stopping it first');
      serverProcess.kill('SIGKILL');
      serverProcess = null;
    }

    serverProcess = spawn('node', [path.join(__dirname, 'backend/server.js')], {
      stdio: 'pipe',
      cwd: __dirname
    });

    serverProcess.stdout.on('data', (data) => {
      console.log(`Server: ${data}`);
      mainWindow?.webContents.send('server-log', data.toString());
    });

    serverProcess.stderr.on('data', (data) => {
      console.error(`Server Error: ${data}`);
      mainWindow?.webContents.send('server-error', data.toString());
    });

    serverProcess.on('close', (code) => {
      console.log(`Server process exited with code ${code}`);
      mainWindow?.webContents.send('server-stopped', code);

      if (code !== 0) {
        notifier.notify({
          title: 'Instagram Live System',
          message: 'Backend server stopped unexpectedly',
          icon: path.join(__dirname, 'assets/icon.png')
        });
      }
    });

    // Get local IP and send mobile access info
    localIP = getLocalIP();
    mainWindow?.webContents.send('server-started');

    // Send mobile access info for production
    if (localIP && localIP !== 'localhost') {
      setTimeout(() => {
        mainWindow?.webContents.send('web-started', { localIP });
      }, 1000);
    }

    notifier.notify({
      title: 'Instagram Live System',
      message: 'Backend server started successfully',
      icon: path.join(__dirname, 'assets/icon.png')
    });

  } catch (error) {
    console.error('Failed to start server:', error);
    mainWindow?.webContents.send('server-error', error.message);
  }
}

// Stop backend server
function stopBackendServer() {
  return new Promise((resolve) => {
    if (serverProcess) {
      // Set up cleanup handler
      const cleanup = () => {
        serverProcess = null;
        mainWindow?.webContents.send('server-stopped', 0);

        notifier.notify({
          title: 'Instagram Live System',
          message: 'Backend server stopped',
          icon: path.join(__dirname, 'assets/icon.png')
        });

        resolve();
      };

      // Listen for process exit
      serverProcess.once('exit', cleanup);

      // Force kill after timeout
      setTimeout(() => {
        if (serverProcess) {
          serverProcess.kill('SIGKILL');
          cleanup();
        }
      }, 5000);

      // Send graceful shutdown signal
      serverProcess.kill('SIGTERM');
    } else {
      resolve();
    }

    // Also stop web server
    if (webProcess) {
      if (webProcess.kill) {
        webProcess.kill();
      } else if (webProcess.server) {
        webProcess.server.close();
      }
      webProcess = null;
      mainWindow?.webContents.send('web-stopped', 0);
    }
  });
}

// App event handlers
app.whenReady().then(() => {
  createMainWindow();

  // Create application menu
  const template = [
    {
      label: 'File',
      submenu: [
        {
          label: 'Settings',
          click: () => {
            mainWindow?.webContents.send('show-settings');
          }
        },
        { type: 'separator' },
        {
          label: 'Open Web Interface',
          click: () => {
            openWebInterface();
          }
        },
        { type: 'separator' },
        {
          label: 'Exit',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'Server',
      submenu: [
        {
          label: 'Start Server',
          click: startBackendServer
        },
        {
          label: 'Stop Server',
          click: stopBackendServer
        }
      ]
    },
    {
      label: 'Help',
      submenu: [
        {
          label: 'About',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'About',
              message: 'CommiLive',
              detail: 'Version 1.0.0\nInstagram Live comment management system'
            });
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
});

app.on('window-all-closed', async () => {
  console.log('🔄 App shutdown initiated - cleaning up processes...');

  // Kill all processes before quitting
  if (serverProcess) {
    console.log('🛑 Stopping server process...');
    serverProcess.kill('SIGTERM');
    // Wait for graceful shutdown
    await new Promise(resolve => setTimeout(resolve, 3000));
    // Force kill if still running
    if (serverProcess && !serverProcess.killed) {
      serverProcess.kill('SIGKILL');
    }
  }
  if (webProcess) {
    console.log('🛑 Stopping web process...');
    if (webProcess.kill) {
      webProcess.kill();
    } else if (webProcess.server) {
      webProcess.server.close();
    }
  }

  // COMPREHENSIVE Chrome cleanup - kill ALL Chrome Testing processes (visible + hidden)
  if (process.platform === 'win32') {
    const { exec } = require('child_process');
    console.log('🧹 Comprehensive Chrome Testing cleanup...');

    // Method 1: Kill ALL Chrome for Testing processes (including headless/hidden)
    await new Promise((resolve) => {
      exec('wmic process where "name=\'chrome.exe\' and commandline like \'%Google Chrome for Testing%\'" delete', (error) => {
        if (error && !error.message.includes('not found')) {
          console.warn('Error killing Chrome for Testing processes:', error.message);
        } else {
          console.log('✅ All Chrome for Testing processes killed');
        }
        resolve();
      });
    });

    // Method 2: Kill Chrome processes from Puppeteer (common paths)
    await new Promise((resolve) => {
      exec('wmic process where "name=\'chrome.exe\' and commandline like \'%puppeteer%\'" delete', (error) => {
        if (error && !error.message.includes('not found')) {
          console.warn('Error killing Puppeteer Chrome processes:', error.message);
        } else {
          console.log('✅ Puppeteer Chrome processes killed');
        }
        resolve();
      });
    });

    // Method 3: Kill processes with testing/automation flags
    await new Promise((resolve) => {
      exec('wmic process where "name=\'chrome.exe\' and (commandline like \'%test-type%\' or commandline like \'%automation%\' or commandline like \'%headless%\')" delete', (error) => {
        if (error && !error.message.includes('not found')) {
          console.warn('Error killing Chrome automation processes:', error.message);
        } else {
          console.log('✅ Chrome automation/headless processes killed');
        }
        resolve();
      });
    });

    // Method 4: Kill Chrome processes with common Puppeteer flags
    await new Promise((resolve) => {
      setTimeout(() => {
        exec('wmic process where "name=\'chrome.exe\' and (commandline like \'%--no-sandbox%\' or commandline like \'%--disable-setuid-sandbox%\' or commandline like \'%--disable-dev-shm-usage%\')" delete', (error) => {
          if (error && !error.message.includes('not found')) {
            console.warn('Error killing Chrome processes with Puppeteer flags:', error.message);
          } else {
            console.log('✅ Chrome processes with Puppeteer flags killed');
          }
          resolve();
        });
      }, 1000);
    });

    // Method 5: Final sweep - kill any Chrome processes from temp directories (often used by testing)
    await new Promise((resolve) => {
      setTimeout(() => {
        exec('wmic process where "name=\'chrome.exe\' and (commandline like \'%temp%\' or commandline like \'%tmp%\')" delete', (error) => {
          if (error && !error.message.includes('not found')) {
            console.warn('Error killing temp Chrome processes:', error.message);
          } else {
            console.log('✅ Temporary Chrome processes killed');
          }
          resolve();
        });
      }, 2000);
    });
  }

  console.log('✅ Process cleanup completed');

  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createMainWindow();
  }
});

// Handle app termination signals for cleanup
process.on('SIGINT', async () => {
  console.log('🛑 SIGINT received - cleaning up Chrome processes...');
  await cleanupChromeProcesses();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('🛑 SIGTERM received - cleaning up Chrome processes...');
  await cleanupChromeProcesses();
  process.exit(0);
});

process.on('exit', () => {
  console.log('🛑 Process exit - chức năng tự động kill Chrome đã bị vô hiệu hóa');
});

// Comprehensive cleanup function for ALL Chrome Testing processes - đã vô hiệu hóa
async function cleanupChromeProcesses() {
  console.log('⚠️ Chức năng tự động kill Chrome đã bị vô hiệu hóa');
  return false;
}

app.on('before-quit', async () => {
  // Kill all processes before quitting
  if (serverProcess) {
    serverProcess.kill('SIGTERM');
    // Wait for graceful shutdown
    await new Promise(resolve => setTimeout(resolve, 3000));
    // Force kill if still running
    if (serverProcess && !serverProcess.killed) {
      serverProcess.kill('SIGKILL');
    }
    serverProcess = null;
  }
  if (webProcess) {
    if (webProcess.kill) {
      webProcess.kill();
    } else if (webProcess.server) {
      webProcess.server.close();
    }
    webProcess = null;
  }

  // Chức năng tự động kill Chrome đã bị vô hiệu hóa
  console.log('⚠️ Chức năng tự động kill Chrome đã bị vô hiệu hóa');
});

// IPC handlers
ipcMain.handle('get-store-value', (event, key, defaultValue) => {
  return store.get(key, defaultValue);
});

ipcMain.handle('set-store-value', (event, key, value) => {
  store.set(key, value);
  return true;
});

ipcMain.handle('start-server', async () => {
  // Ensure any existing server is stopped first
  if (serverProcess) {
    await stopBackendServer();
    // Wait a bit for cleanup
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  startBackendServer();
  return true;
});

ipcMain.handle('stop-server', async () => {
  await stopBackendServer();
  return true;
});

ipcMain.handle('show-notification', (event, options) => {
  notifier.notify({
    title: options.title || 'CommiLive',
    message: options.message,
    icon: path.join(__dirname, 'assets/icon.png'),
    ...options
  });
  return true;
});

ipcMain.handle('open-web-interface', () => {
  openWebInterface();
  return true;
});

ipcMain.handle('get-local-ip', () => {
  return getLocalIP();
});

ipcMain.handle('generate-qr-code', async (event, url) => {
  try {
    const qrCodeDataURL = await QRCode.toDataURL(url, {
      width: 200,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    });
    return qrCodeDataURL;
  } catch (error) {
    console.error('Failed to generate QR code:', error);
    return null;
  }
});

// Handle app errors
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);

  notifier.notify({
    title: 'Instagram Live System - Error',
    message: 'An unexpected error occurred',
    icon: path.join(__dirname, 'assets/icon.png')
  });
});

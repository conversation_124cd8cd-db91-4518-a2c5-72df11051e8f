import React, { useState, useEffect } from 'react';
import { Wifi, WifiOff, RefreshCw, X } from 'lucide-react';

const ConnectionStatus = ({ isConnected, onReconnect }) => {
  const [showBanner, setShowBanner] = useState(false);
  const [isReconnecting, setIsReconnecting] = useState(false);

  useEffect(() => {
    if (!isConnected) {
      setShowBanner(true);
    } else {
      setShowBanner(false);
      setIsReconnecting(false);
    }
  }, [isConnected]);

  const handleReconnect = async () => {
    setIsReconnecting(true);
    try {
      await onReconnect();
    } catch (error) {
      console.error('Reconnection failed:', error);
    }
    // Reset reconnecting state after 3 seconds regardless of result
    setTimeout(() => setIsReconnecting(false), 3000);
  };

  const handleDismiss = () => {
    setShowBanner(false);
  };

  if (!showBanner) {
    return null;
  }

  return (
    <div className={`
      fixed top-0 left-0 right-0 z-50 transition-all duration-300 ease-in-out
      ${showBanner ? 'translate-y-0' : '-translate-y-full'}
    `}>
      <div className="bg-red-500 text-white px-4 py-3 shadow-lg">
        <div className="flex items-center justify-between max-w-7xl mx-auto">
          <div className="flex items-center space-x-3">
            <WifiOff className="h-5 w-5 flex-shrink-0" />
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium">
                Mất kết nối với server
              </p>
              <p className="text-xs opacity-90">
                Vui lòng kiểm tra kết nối mạng và thử lại
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={handleReconnect}
              disabled={isReconnecting}
              className="inline-flex items-center px-3 py-1 border border-white border-opacity-30 rounded text-xs font-medium hover:bg-white hover:bg-opacity-10 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              <RefreshCw className={`h-3 w-3 mr-1 ${isReconnecting ? 'animate-spin' : ''}`} />
              {isReconnecting ? 'Đang kết nối...' : 'Thử lại'}
            </button>
            
            <button
              onClick={handleDismiss}
              className="p-1 hover:bg-white hover:bg-opacity-10 rounded focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 transition-colors duration-200"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConnectionStatus;

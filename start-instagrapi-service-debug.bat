@echo off
echo ================================================
echo Starting Instagrapi Service with DEBUG mode...
echo ================================================
cd /d "%~dp0"

echo Installing required packages if not already installed...
pip install instagrapi flask requests

echo Setting up DEBUG environment...
set FLASK_ENV=development
set FLASK_DEBUG=1
set NODE_SERVER_PORT=3001

echo Starting Python service in DEBUG mode...
python -c "import os; print(f'NODE SERVER URL: http://localhost:{os.environ.get(\"NODE_SERVER_PORT\", \"3001\")}'); from src.backend.services.InstagrapiService import app; app.run(debug=True, port=5000)"

echo.
echo Service terminated. Press any key to exit...
pause > nul 
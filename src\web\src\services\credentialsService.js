// Service để quản lý thông tin đăng nhập đã lưu
class CredentialsService {
  constructor() {
    this.STORAGE_KEY = 'saved_instagram_credentials';
  }

  // L<PERSON>u thông tin đăng nhập
  saveCredentials(username, password, type = 'default') {
    try {
      const credentials = {
        username: username.trim(),
        password: password.trim(),
        type: type, // 'default', 'instagrapi', etc.
        savedAt: new Date().toISOString()
      };

      localStorage.setItem(`${this.STORAGE_KEY}_${type}`, JSON.stringify(credentials));
      return true;
    } catch (error) {
      console.error('Error saving credentials:', error);
      return false;
    }
  }

  // Lấy thông tin đăng nhập đã lưu
  getSavedCredentials(type = 'default') {
    try {
      const saved = localStorage.getItem(`${this.STORAGE_KEY}_${type}`);
      if (saved) {
        return JSON.parse(saved);
      }
      return null;
    } catch (error) {
      console.error('Error loading saved credentials:', error);
      return null;
    }
  }

  // <PERSON>ểm tra có thông tin đăng nhập đã lưu không
  hasSavedCredentials(type = 'default') {
    const credentials = this.getSavedCredentials(type);
    return credentials && credentials.username && credentials.password;
  }

  // Xóa thông tin đăng nhập đã lưu
  clearSavedCredentials(type = 'default') {
    try {
      localStorage.removeItem(`${this.STORAGE_KEY}_${type}`);
      return true;
    } catch (error) {
      console.error('Error clearing saved credentials:', error);
      return false;
    }
  }

  // Xóa tất cả thông tin đăng nhập đã lưu
  clearAllSavedCredentials() {
    try {
      const keys = Object.keys(localStorage).filter(key =>
        key.startsWith(this.STORAGE_KEY)
      );
      keys.forEach(key => localStorage.removeItem(key));
      return true;
    } catch (error) {
      console.error('Error clearing all saved credentials:', error);
      return false;
    }
  }

  // Tự động đăng nhập với thông tin đã lưu
  async autoLogin(type = 'default', loginFunction) {
    const credentials = this.getSavedCredentials(type);
    if (credentials && loginFunction) {
      try {
        return await loginFunction(credentials.username, credentials.password);
      } catch (error) {
        console.error('Auto login failed:', error);
        return false;
      }
    }
    return false;
  }
}

// Export singleton instance
export default new CredentialsService();

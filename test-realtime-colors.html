<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Realtime Comment Colors</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .comment-item {
            padding: 12px;
            margin: 8px 0;
            border: 1px solid #ddd;
            border-radius: 6px;
            transition: background-color 0.3s ease;
        }

        .status-info {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
        }

        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
        }

        button {
            padding: 8px 16px;
            margin: 4px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background: #007bff;
            color: white;
        }

        button:hover {
            background: #0056b3;
        }

        .log {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 20px;
        }

        .status-pending {
            background-color: #dbeafe;
        }

        .status-processing {
            background-color: #fce7f3;
        }

        .status-completed {
            background-color: #bbf7d0;
        }

        .status-failed {
            background-color: #fecaca;
        }

        .status-cancelled {
            background-color: #f3f4f6;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>Test Realtime Comment Colors</h1>

        <div class="controls">
            <h3>Kiểm tra màu sắc theo trạng thái:</h3>
            <button onclick="testColors()">Test All Colors</button>
            <button onclick="connectSocket()">Connect Socket</button>
            <button onclick="fetchQueue()">Fetch Queue</button>
            <button onclick="clearLog()">Clear Log</button>
        </div>

        <div id="comments-container">
            <!-- Comments will be rendered here -->
        </div>

        <div class="log" id="log">
            <div>Log messages will appear here...</div>
        </div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script>
        let socket = null;
        let messageQueue = [];
        let comments = [
            { id: 'test1', username: 'user1', text: 'Test comment 1' },
            { id: 'test2', username: 'user2', text: 'Test comment 2' },
            { id: 'test3', username: 'user3', text: 'Test comment 3' }
        ];

        function log(message) {
            const logEl = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logEl.innerHTML += `<div>[${time}] ${message}</div>`;
            logEl.scrollTop = logEl.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '<div>Log cleared...</div>';
        }

        function connectSocket() {
            if (socket) {
                socket.disconnect();
            }

            socket = io('http://localhost:3001');

            socket.on('connect', () => {
                log('✅ Socket connected: ' + socket.id);
            });

            socket.on('disconnect', () => {
                log('❌ Socket disconnected');
            });

            socket.on('message-status-update', (data) => {
                log('📝 Message status update: ' + JSON.stringify(data));
                updateMessageQueue(data);
            });

            socket.on('messageStatusUpdate', (data) => {
                log('📝 Message status update (alt): ' + JSON.stringify(data));
                updateMessageQueue(data);
            });

            socket.on('message-queue-updated', () => {
                log('🔄 Queue updated - refetching...');
                fetchQueue();
            });

            socket.on('print-success', (data) => {
                log('🖨️ Print success: ' + JSON.stringify(data));
            });
        }

        function updateMessageQueue(data) {
            messageQueue = messageQueue.map(msg =>
                msg.id === data.id ? { ...msg, status: data.status } : msg
            );
            renderComments();
        }

        async function fetchQueue() {
            try {
                log('🔍 Fetching queue...');
                const response = await fetch('http://localhost:3001/api/message-queue?status=all');
                const data = await response.json();

                if (data.success) {
                    messageQueue = data.messages;
                    log(`✅ Queue fetched: ${messageQueue.length} messages`);
                    renderComments();
                } else {
                    log('❌ Failed to fetch queue: ' + data.error);
                }
            } catch (error) {
                log('❌ Error fetching queue: ' + error.message);
            }
        }

        function getQueueStatusForComment(comment) {
            const found = messageQueue.find(m =>
                m.comment_id === comment.id ||
                (m.username === comment.username && m.original_comment === comment.text)
            );
            return found ? found.status : null;
        }

        function getColorForStatus(status) {
            switch (status) {
                case 'pending': return '#dbeafe';
                case 'processing': return '#fce7f3';
                case 'completed': return '#bbf7d0';
                case 'failed': return '#fecaca';
                case 'cancelled': return '#f3f4f6';
                default: return '#ffffff';
            }
        }

        function renderComments() {
            const container = document.getElementById('comments-container');
            container.innerHTML = '';

            comments.forEach(comment => {
                const status = getQueueStatusForComment(comment);
                const color = getColorForStatus(status);

                const div = document.createElement('div');
                div.className = 'comment-item';
                div.style.backgroundColor = color;

                div.innerHTML = `
                    <div><strong>@${comment.username}</strong>: ${comment.text}</div>
                    <div class="status-info">
                        Status: ${status || 'none'} | Color: ${color}
                    </div>
                `;

                container.appendChild(div);
            });
        }

        function testColors() {
            log('🎨 Testing all color states...');

            // Simulate different queue states
            messageQueue = [
                { id: 'msg1', comment_id: 'test1', username: 'user1', original_comment: 'Test comment 1', status: 'pending' },
                { id: 'msg2', comment_id: 'test2', username: 'user2', original_comment: 'Test comment 2', status: 'processing' },
                { id: 'msg3', comment_id: 'test3', username: 'user3', original_comment: 'Test comment 3', status: 'completed' }
            ];

            renderComments();
            log('✅ Test colors applied');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            log('🚀 Page loaded');
            renderComments();
        });
    </script>
</body>

</html>
import React, { useState, useEffect } from 'react';
import { Inbox, X, Trash2, RefreshCw, AlertCircle, CheckCircle, Info, CornerUpLeft } from 'lucide-react';
import { useSocket } from '../contexts/SocketContext';
import { getApiUrl } from '../config/api';
import toast from 'react-hot-toast';

const MessageQueueManager = () => {
    const { socket, isConnected } = useSocket();
    const [messages, setMessages] = useState([]);
    const [stats, setStats] = useState({
        pending: 0,
        processing: 0,
        completed: 0,
        failed: 0,
        total: 0
    });
    const [isLoading, setIsLoading] = useState(false);
    const [isDeleting, setIsDeleting] = useState(false);
    const [isValidating, setIsValidating] = useState(false);
    const [templateValidation, setTemplateValidation] = useState(null);
    const [showValidationResults, setShowValidationResults] = useState(false);

    // Load message queue on component mount
    useEffect(() => {
        loadMessageQueue();
    }, []);

    // Listen for socket events
    useEffect(() => {
        if (socket && isConnected) {
            // Reload queue when it's updated
            socket.on('message-queue-updated', () => {
                loadMessageQueue();
            });

            // Reload queue when a new message is added
            socket.on('message-added-to-queue', () => {
                loadMessageQueue();
            });

            // Reload queue when message status changes
            socket.on('messageStatusUpdate', (updatedMessage) => {
                // Optimize by updating the specific message instead of reloading the entire queue
                setMessages(prev =>
                    prev.map(msg =>
                        msg.id === updatedMessage.id ? { ...msg, status: updatedMessage.status } : msg
                    )
                );
                // Still reload stats
                loadQueueStats();
            });

            // Handle message unsent
            socket.on('message-unsent', (data) => {
                console.log('Message unsent in queue manager:', data);
                // Remove message from queue or mark as unsent
                setMessages(prev => prev.filter(msg =>
                    msg.instagram_message_id !== data.instagram_message_id
                ));
                // Reload stats
                loadQueueStats();
            });

            return () => {
                socket.off('message-queue-updated');
                socket.off('message-added-to-queue');
                socket.off('messageStatusUpdate');
                socket.off('message-unsent');
            };
        }
    }, [socket, isConnected]);

    // Load message queue data
    const loadMessageQueue = async () => {
        setIsLoading(true);
        try {
            const response = await fetch(getApiUrl('/api/message-queue?status=all'));
            const data = await response.json();

            if (data.success) {
                setMessages(data.messages);
                setStats(data.stats);
            } else {
                throw new Error(data.error || 'Không thể tải dữ liệu hàng chờ tin nhắn');
            }
        } catch (error) {
            console.error('Failed to load message queue:', error);
            toast.error('Không thể tải dữ liệu hàng chờ tin nhắn');
        } finally {
            setIsLoading(false);
        }
    };

    // Load just the queue stats
    const loadQueueStats = async () => {
        try {
            const response = await fetch(getApiUrl('/api/message-queue?limit=1'));
            const data = await response.json();

            if (data.success) {
                setStats(data.stats);
            }
        } catch (error) {
            console.error('Failed to load queue stats:', error);
        }
    };

    // Validate templates in queue
    const validateTemplates = async () => {
        setIsValidating(true);
        setTemplateValidation(null);

        try {
            const response = await fetch(getApiUrl('/api/message-queue/validate-templates'));
            const data = await response.json();

            if (data.success) {
                setTemplateValidation(data);
                setShowValidationResults(true);

                if (data.mismatches > 0) {
                    toast.error(`Phát hiện ${data.mismatches} tin nhắn có template không hợp lệ!`);
                } else {
                    toast.success('Tất cả tin nhắn đều có template hợp lệ');
                }
            } else {
                throw new Error(data.error || 'Không thể kiểm tra template');
            }
        } catch (error) {
            console.error('Failed to validate templates:', error);
            toast.error('Không thể kiểm tra template: ' + error.message);
        } finally {
            setIsValidating(false);
        }
    };

    // Get detailed information about messages
    const getMessageDetails = async () => {
        try {
            const response = await fetch(getApiUrl('/api/message-queue/details'));
            const data = await response.json();

            if (data.success) {
                console.log('Message queue details:', data);
                toast.success(`Đã tải chi tiết ${data.messages.length} tin nhắn (xem console)`);
            } else {
                throw new Error(data.error || 'Không thể tải chi tiết tin nhắn');
            }
        } catch (error) {
            console.error('Failed to get message details:', error);
            toast.error('Không thể tải chi tiết tin nhắn: ' + error.message);
        }
    };

    // Delete a single message from the queue
    const deleteMessage = async (id) => {
        try {
            const response = await fetch(getApiUrl(`/api/message-queue/${id}`), {
                method: 'DELETE',
            });

            const data = await response.json();
            if (data.success) {
                toast.success('Đã xoá tin nhắn khỏi hàng chờ');

                // Update UI optimistically
                setMessages(prev => prev.filter(msg => msg.id !== id));
                setStats(prev => ({
                    ...prev,
                    pending: Math.max(0, prev.pending - 1),
                    total: Math.max(0, prev.total - 1)
                }));
            } else {
                throw new Error(data.error || 'Không thể xoá tin nhắn');
            }
        } catch (error) {
            console.error('Failed to delete message:', error);
            toast.error('Không thể xoá tin nhắn: ' + error.message);
        }
    };

    // Clear all messages from queue
    const clearQueue = async () => {
        if (!window.confirm('Bạn có chắc chắn muốn xoá TẤT CẢ tin nhắn trong hàng chờ (bao gồm cả tin đã hoàn thành, đang xử lý, và thất bại)?')) {
            return;
        }

        setIsDeleting(true);
        try {
            const response = await fetch(getApiUrl('/api/message-queue'), {
                method: 'DELETE',
            });

            const data = await response.json();
            if (data.success) {
                toast.success(data.message || 'Đã xoá tất cả tin nhắn trong hàng chờ');
                setMessages([]);
                setStats({
                    pending: 0,
                    processing: 0,
                    completed: 0,
                    failed: 0,
                    total: 0
                });
            } else {
                throw new Error(data.error || 'Không thể xoá tin nhắn');
            }
        } catch (error) {
            console.error('Failed to clear queue:', error);
            toast.error('Không thể xoá tin nhắn: ' + error.message);
        } finally {
            setIsDeleting(false);
        }
    };

    // Unsend message - handles both pending and completed messages
    const unsendMessage = async (messageId, threadId, instaMessageId, messageStatus) => {
        // Dùng confirm text giống nhau cho cả 2 trường hợp
        if (!window.confirm('Bạn có chắc chắn muốn thu hồi tin nhắn này?')) {
            return;
        }

        // Đối với pending messages, sử dụng messageId (queue ID)
        // Đối với completed messages, sử dụng instaMessageId (Instagram message ID)
        const apiId = messageStatus === 'pending' ? messageId : instaMessageId;

        if (!apiId) {
            toast.error('Không thể thu hồi tin nhắn này - thiếu thông tin ID');
            return;
        }

        try {
            const response = await fetch(getApiUrl(`/api/instagram/unsend/${apiId}`), {
                method: 'DELETE',
            });

            const data = await response.json();
            if (data.success) {
                const actionText = data.type === 'pending' ? 'xóa khỏi hàng chờ' : 'thu hồi tin nhắn';
                const historyText = data.deletedHistoryRecords > 0 ? ` và xóa ${data.deletedHistoryRecords} record khỏi lịch sử` : '';
                toast.success(`Đã ${actionText} thành công${historyText}`);

                // Cập nhật UI - xóa message khỏi danh sách
                setMessages(prev => prev.filter(msg => msg.id !== messageId));
            } else {
                throw new Error(data.error || 'Không thể thu hồi tin nhắn');
            }
        } catch (error) {
            console.error('Failed to unsend message:', error);
            toast.error('Không thể thu hồi tin nhắn: ' + error.message);
        }
    };

    // Format timestamp
    const formatTime = (timestamp) => {
        if (!timestamp) return 'N/A';

        const date = new Date(timestamp);
        return date.toLocaleTimeString('vi-VN', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    };

    // Get status badge based on message status
    const getStatusBadge = (status) => {
        switch (status) {
            case 'pending':
                return <span className="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">Đang chờ</span>;
            case 'processing':
                return <span className="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">Đang xử lý</span>;
            case 'completed':
                return <span className="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">Hoàn thành</span>;
            case 'failed':
                return <span className="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800">Thất bại</span>;
            case 'cancelled':
                return <span className="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800">Đã hủy</span>;
            default:
                return <span className="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800">{status}</span>;
        }
    };

    return (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                    <Inbox className="h-5 w-5 text-blue-600" />
                    <h2 className="text-lg font-medium text-gray-900">Hàng chờ tin nhắn</h2>
                </div>

                <div className="flex items-center space-x-2">
                    <button
                        onClick={loadMessageQueue}
                        disabled={isLoading}
                        className="flex items-center px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
                    >
                        <RefreshCw className={`h-4 w-4 mr-1 ${isLoading ? 'animate-spin' : ''}`} />
                        <span>Làm mới</span>
                    </button>

                    <button
                        onClick={validateTemplates}
                        disabled={isValidating || !messages.length}
                        className="flex items-center px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 disabled:opacity-50"
                    >
                        <CheckCircle className={`h-4 w-4 mr-1 ${isValidating ? 'animate-spin' : ''}`} />
                        <span>Kiểm tra template</span>
                    </button>

                    <button
                        onClick={getMessageDetails}
                        disabled={!messages.length}
                        className="flex items-center px-3 py-1 text-sm bg-green-100 text-green-700 rounded hover:bg-green-200 disabled:opacity-50"
                    >
                        <Info className="h-4 w-4 mr-1" />
                        <span>Xem chi tiết</span>
                    </button>

                    <button
                        onClick={clearQueue}
                        disabled={isLoading || isDeleting || !messages.length}
                        className="flex items-center px-3 py-1 text-sm bg-red-100 text-red-700 rounded hover:bg-red-200 disabled:opacity-50"
                    >
                        <Trash2 className="h-4 w-4 mr-1" />
                        <span>Xóa tất cả tin nhắn</span>
                    </button>
                </div>
            </div>

            {showValidationResults && templateValidation && (
                <div className={`mb-4 p-4 rounded-lg ${templateValidation.mismatches > 0 ? 'bg-red-50' : 'bg-green-50'}`}>
                    <div className="flex justify-between items-start">
                        <div>
                            <h3 className={`text-md font-semibold ${templateValidation.mismatches > 0 ? 'text-red-700' : 'text-green-700'}`}>
                                {templateValidation.mismatches > 0 ? 'Phát hiện vấn đề với template' : 'Tất cả template hợp lệ'}
                            </h3>
                            <p className="text-sm mt-1">
                                {templateValidation.mismatches > 0
                                    ? `Có ${templateValidation.mismatches} tin nhắn có template không hợp lệ (trong tổng số ${templateValidation.total} tin nhắn).`
                                    : `Tất cả ${templateValidation.total} tin nhắn đều có template hợp lệ.`}
                            </p>
                        </div>
                        <button
                            className="text-gray-500 hover:text-gray-700"
                            onClick={() => setShowValidationResults(false)}
                        >
                            <X className="h-4 w-4" />
                        </button>
                    </div>

                    {templateValidation.mismatches > 0 && (
                        <div className="mt-3">
                            <p className="text-sm font-medium mb-1">Chi tiết các tin nhắn không hợp lệ:</p>
                            <ul className="text-xs text-red-700 pl-5 list-disc">
                                {templateValidation.mismatchedMessages.map((msg, index) => (
                                    <li key={index}>
                                        @{msg.username}: template "{msg.template_name}" với loại khách hàng "{msg.customer_type}"{' '}
                                        {msg.actual_customer_type
                                            ? `nhưng template tồn tại với loại khách hàng "${msg.actual_customer_type}"`
                                            : 'nhưng template không tồn tại'}
                                    </li>
                                ))}
                            </ul>
                        </div>
                    )}
                </div>
            )}

            <div className="grid grid-cols-4 gap-3 mb-4">
                <div className="bg-blue-50 p-3 rounded-lg">
                    <p className="text-xs text-blue-600 font-medium uppercase">Đang chờ</p>
                    <p className="text-2xl font-bold text-blue-700">{stats.pending}</p>
                </div>
                <div className="bg-yellow-50 p-3 rounded-lg">
                    <p className="text-xs text-yellow-600 font-medium uppercase">Đang xử lý</p>
                    <p className="text-2xl font-bold text-yellow-700">{stats.processing}</p>
                </div>
                <div className="bg-green-50 p-3 rounded-lg">
                    <p className="text-xs text-green-600 font-medium uppercase">Hoàn thành</p>
                    <p className="text-2xl font-bold text-green-700">{stats.completed}</p>
                </div>
                <div className="bg-red-50 p-3 rounded-lg">
                    <p className="text-xs text-red-600 font-medium uppercase">Thất bại</p>
                    <p className="text-2xl font-bold text-red-700">{stats.failed}</p>
                </div>
            </div>

            {isLoading ? (
                <div className="flex justify-center items-center py-10">
                    <div className="animate-pulse flex items-center space-x-2">
                        <div className="h-4 w-4 bg-blue-400 rounded-full"></div>
                        <span className="text-gray-500">Đang tải dữ liệu...</span>
                    </div>
                </div>
            ) : messages.length === 0 ? (
                <div className="text-center py-10 border border-gray-100 rounded-lg">
                    <Inbox className="h-10 w-10 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-500">Không có tin nhắn nào trong hàng chờ</p>
                </div>
            ) : (
                <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                            <tr>
                                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Username
                                </th>
                                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Bình luận gốc
                                </th>
                                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Template / Loại KH
                                </th>
                                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Trạng thái
                                </th>
                                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Thời gian
                                </th>
                                <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Thao tác
                                </th>
                            </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                            {messages.map((message) => (
                                <tr key={message.id} className="hover:bg-gray-50">
                                    <td className="px-4 py-3 text-sm text-gray-900 font-medium">
                                        @{message.username}
                                    </td>
                                    <td className="px-4 py-3 text-sm text-gray-600 max-w-[300px] truncate">
                                        {message.original_comment}
                                    </td>
                                    <td className="px-4 py-3 text-sm text-gray-600">
                                        {message.template_name}
                                        {message.customer_type && <span className="text-xs ml-1 text-gray-500">({message.customer_type})</span>}
                                    </td>
                                    <td className="px-4 py-3 text-sm">
                                        {getStatusBadge(message.status)}
                                    </td>
                                    <td className="px-4 py-3 text-sm text-gray-500">
                                        {formatTime(message.created_at)}
                                    </td>
                                    <td className="px-4 py-3 text-sm text-right">
                                        {/* Nút thu hồi cho pending messages */}
                                        {message.status === 'pending' && (
                                            <button
                                                onClick={() => unsendMessage(message.id, null, null, 'pending')}
                                                className="text-blue-600 hover:text-blue-900"
                                                title="Thu hồi tin nhắn"
                                            >
                                                <CornerUpLeft className="h-4 w-4" />
                                            </button>
                                        )}

                                        {/* Nút thu hồi cho completed messages */}
                                        {message.status === 'completed' && message.instagram_message_id && (
                                            <button
                                                onClick={() => unsendMessage(message.id, message.thread_id, message.instagram_message_id, 'completed')}
                                                className="text-blue-600 hover:text-blue-900"
                                                title="Thu hồi tin nhắn"
                                            >
                                                <CornerUpLeft className="h-4 w-4" />
                                            </button>
                                        )}
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            )}

            <div className="mt-4 text-xs text-gray-500">
                <p className="flex items-center">
                    <AlertCircle className="h-3 w-3 mr-1" />
                    Tin nhắn trong hàng chờ sẽ tự động được gửi theo thứ tự
                </p>
            </div>
        </div>
    );
};

export default MessageQueueManager; 
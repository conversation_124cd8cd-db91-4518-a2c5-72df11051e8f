import React, { useState } from 'react';
import { RefreshCw, Cloud, AlertCircle } from 'lucide-react';
import { getApiUrl } from '../config/api';
import { showToast, showErrorToast } from '../utils/toastManager';

const SyncButton = ({ 
  type = 'customers', // 'customers', 'printed-history', 'all'
  size = 'sm',
  showText = true,
  onSyncComplete = null,
  className = ''
}) => {
  const [isLoading, setIsLoading] = useState(false);

  // Size configurations
  const sizeConfig = {
    xs: { icon: 'h-3 w-3', text: 'text-xs', padding: 'px-2 py-1' },
    sm: { icon: 'h-4 w-4', text: 'text-sm', padding: 'px-3 py-2' },
    md: { icon: 'h-5 w-5', text: 'text-base', padding: 'px-4 py-2' },
    lg: { icon: 'h-6 w-6', text: 'text-lg', padding: 'px-6 py-3' }
  };

  const config = sizeConfig[size] || sizeConfig.sm;

  // Get sync endpoint and text based on type
  const getSyncConfig = () => {
    switch (type) {
      case 'customers':
        return {
          endpoint: '/api/mongodb/sync-customers',
          text: 'Sync khách hàng',
          loadingText: 'Đang sync khách hàng...',
          successText: 'Đã sync khách hàng'
        };
      case 'printed-history':
        return {
          endpoint: '/api/mongodb/sync-printed-history',
          text: 'Sync lịch sử in',
          loadingText: 'Đang sync lịch sử in...',
          successText: 'Đã sync lịch sử in'
        };
      case 'all':
        return {
          endpoint: '/api/mongodb/smart-sync',
          text: 'Sync tất cả',
          loadingText: 'Đang sync tất cả...',
          successText: 'Đã sync tất cả dữ liệu'
        };
      default:
        return {
          endpoint: '/api/mongodb/smart-sync',
          text: 'Sync',
          loadingText: 'Đang sync...',
          successText: 'Đã sync'
        };
    }
  };

  const syncConfig = getSyncConfig();

  const handleSync = async () => {
    if (isLoading) return;

    setIsLoading(true);
    
    try {
      const response = await fetch(getApiUrl(syncConfig.endpoint), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      if (data.success) {
        showToast(syncConfig.successText);
        
        // Call onSyncComplete callback if provided
        if (onSyncComplete) {
          onSyncComplete(data);
        }
      } else {
        throw new Error(data.error || 'Sync failed');
      }
    } catch (error) {
      console.error('Sync error:', error);
      showErrorToast(`Lỗi sync: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <button
      onClick={handleSync}
      disabled={isLoading}
      className={`
        inline-flex items-center space-x-2 
        ${config.padding}
        bg-blue-600 hover:bg-blue-700 
        disabled:bg-blue-400 disabled:cursor-not-allowed
        text-white rounded-lg transition-colors
        ${config.text}
        ${className}
      `}
      title={isLoading ? syncConfig.loadingText : syncConfig.text}
    >
      {/* Icon */}
      <RefreshCw 
        className={`${config.icon} ${isLoading ? 'animate-spin' : ''}`}
      />
      
      {/* Text */}
      {showText && (
        <span>
          {isLoading ? syncConfig.loadingText : syncConfig.text}
        </span>
      )}
    </button>
  );
};

export default SyncButton;

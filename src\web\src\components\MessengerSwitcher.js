import React, { useState, useEffect } from 'react';
import { showToast } from '../utils/toastManager';

const LOCAL_STORAGE_KEY = 'messenger_mode_instagrapi';

const MessengerSwitcher = ({ onSwitchMode }) => {
    const [useInstagrapi, setUseInstagrapi] = useState(false);

    useEffect(() => {
        // Lấy cài đặt hiện tại từ server (ưu tiên)
        checkCurrentMode();
    }, []); // eslint-disable-line react-hooks/exhaustive-deps

    const checkCurrentMode = async () => {
        try {
            const response = await fetch('/api/messenger/get-mode');
            const data = await response.json();

            if (data.success) {
                setUseInstagrapi(data.useInstagrapi);
                // Cập nhật component cha
                if (onSwitchMode) {
                    onSwitchMode(data.useInstagrapi);
                }
                // Sync với localStorage để backup
                localStorage.setItem(LOCAL_STORAGE_KEY, data.useInstagrapi.toString());
            } else {
                // Fallback to localStorage nếu API fail
                const savedMode = localStorage.getItem(LOCAL_STORAGE_KEY);
                if (savedMode !== null) {
                    const useInstagrapi = savedMode === 'true';
                    setUseInstagrapi(useInstagrapi);
                    if (onSwitchMode) {
                        onSwitchMode(useInstagrapi);
                    }
                }
            }
        } catch (error) {
            console.error('Failed to check current messenger mode:', error);
            // Fallback to localStorage nếu API fail
            const savedMode = localStorage.getItem(LOCAL_STORAGE_KEY);
            if (savedMode !== null) {
                const useInstagrapi = savedMode === 'true';
                setUseInstagrapi(useInstagrapi);
                if (onSwitchMode) {
                    onSwitchMode(useInstagrapi);
                }
            }
        }
    };

    const toggleMessengerMode = async () => {
        try {
            const response = await fetch('/api/messenger/set-mode', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ useInstagrapi: !useInstagrapi })
            });

            const result = await response.json();

            if (result.success) {
                // Lưu trạng thái mới vào state và localStorage
                setUseInstagrapi(result.useInstagrapi);
                localStorage.setItem(LOCAL_STORAGE_KEY, result.useInstagrapi.toString());

                showToast(`Đã chuyển sang sử dụng ${result.useInstagrapi ? 'Instagrapi API' : 'Chrome Testing'}`, 'success');

                // Gọi callback để thông báo cho component cha
                if (onSwitchMode) {
                    onSwitchMode(result.useInstagrapi);
                }
            } else {
                showToast('Không thể chuyển đổi phương thức gửi tin nhắn', 'error');
            }
        } catch (error) {
            console.error('Failed to toggle messenger mode:', error);
            showToast('Lỗi khi chuyển đổi phương thức gửi tin nhắn', 'error');
        }
    };

    return (
        <div className="flex items-center mb-4">
            <div className="flex-1">
                <h3 className="text-md font-semibold">Phương thức gửi tin nhắn</h3>
                <p className="text-sm text-gray-500 mt-1">
                    Chọn phương thức để gửi tin nhắn Instagram
                </p>
            </div>
            <div className="flex items-center space-x-2">
                <span className={`text-sm ${!useInstagrapi ? 'font-medium' : 'text-gray-500'}`}>Chrome Testing</span>
                <button
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none ${useInstagrapi ? 'bg-blue-600' : 'bg-gray-200'
                        }`}
                    onClick={toggleMessengerMode}
                >
                    <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${useInstagrapi ? 'translate-x-6' : 'translate-x-1'
                            }`}
                    />
                </button>
                <span className={`text-sm ${useInstagrapi ? 'font-medium' : 'text-gray-500'}`}>Instagrapi API</span>
            </div>
        </div>
    );
};

export default MessengerSwitcher; 
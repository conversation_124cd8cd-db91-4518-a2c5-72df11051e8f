import React, { useState, useEffect } from 'react';
import { Package, X } from 'lucide-react';
import { useSocket } from '../contexts/SocketContext';
import { useCancelSettings } from '../contexts/CancelSettingsContext';
import { showPrintToast, showErrorToast } from '../utils/toastManager';

const BackupButton = ({ comment, disabled = false, size = 'xs', regularCustomers = new Set() }) => {
  const { printComment, isConnected } = useSocket();
  const { settings, isCancelEnabled } = useCancelSettings();
  const [isLoading, setIsLoading] = useState(false);
  const [showBackupCancel, setShowBackupCancel] = useState(false);
  const [backupCancelTimeout, setBackupCancelTimeout] = useState(null);
  const [progressInterval, setProgressInterval] = useState(null);
  const [progress, setProgress] = useState(100);

  const handleBackupClick = () => {
    if (!isConnected) {
      showErrorToast('Không có kết nối với server');
      return;
    }

    // Check if cancel is enabled for backup action
    if (!isCancelEnabled('backup')) {
      // If cancel is disabled, execute backup immediately
      executeBackup();
      return;
    }

    setShowBackupCancel(true);
    setProgress(100);

    // Clear any existing timeouts/intervals
    if (backupCancelTimeout) {
      clearTimeout(backupCancelTimeout);
    }
    if (progressInterval) {
      clearInterval(progressInterval);
    }

    // Use duration from settings
    const duration = settings.duration;
    const interval = 16; // ~60fps
    const steps = duration / interval;
    const progressStep = 100 / steps;
    
    let currentProgress = 100;
    const newProgressInterval = setInterval(() => {
      currentProgress -= progressStep;
      if (currentProgress <= 0) {
        clearInterval(newProgressInterval);
        setProgressInterval(null);
        setProgress(0);
        setShowBackupCancel(false);
        executeBackup();
      } else {
        setProgress(currentProgress);
      }
    }, interval);

    setProgressInterval(newProgressInterval);

    // Set timeout as backup (should not be needed with progress animation)
    const timeout = setTimeout(() => {
      if (newProgressInterval) {
        clearInterval(newProgressInterval);
        setProgressInterval(null);
      }
      setShowBackupCancel(false);
      executeBackup();
    }, duration);

    setBackupCancelTimeout(timeout);
  };

  const handleCancel = () => {
    setShowBackupCancel(false);
    setProgress(100);
    if (backupCancelTimeout) {
      clearTimeout(backupCancelTimeout);
      setBackupCancelTimeout(null);
    }
    if (progressInterval) {
      clearInterval(progressInterval);
      setProgressInterval(null);
    }
  };

  const executeBackup = async () => {
    setIsLoading(true);

    try {
      // Send comment with backup template type
      const response = await printComment(comment, 'backup_notification');

      const isRegularCustomer = regularCustomers.has(comment.username);
      const customerTypeText = isRegularCustomer ? 'khách cũ' : 'khách mới';

      // Show success toast
      showPrintToast(`Đã dự bị bình luận của @${comment.username} (${customerTypeText})`);

      // Log the response for debugging
      console.log('Backup response:', response);
    } catch (error) {
      showErrorToast(`Lỗi khi dự bị bình luận: ` + error.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Cleanup timeouts and intervals on unmount
  useEffect(() => {
    return () => {
      if (backupCancelTimeout) {
        clearTimeout(backupCancelTimeout);
      }
      if (progressInterval) {
        clearInterval(progressInterval);
      }
    };
  }, [backupCancelTimeout, progressInterval]);

  const getButtonSize = () => {
    switch (size) {
      case 'xs':
        return 'px-1 py-0.5 text-xs';
      case 'sm':
        return 'px-1.5 py-0.5 text-xs';
      case 'md':
        return 'px-3 py-1.5 text-sm';
      case 'lg':
        return 'px-4 py-2 text-base touch-manipulation';
      case 'xl':
        return 'px-6 py-3 text-lg touch-manipulation';
      default:
        return 'px-1 py-0.5 text-xs';
    }
  };

  const getIconSize = () => {
    switch (size) {
      case 'xs':
        return 'h-2.5 w-2.5';
      case 'sm':
        return 'h-3 w-3';
      case 'md':
        return 'h-4 w-4';
      case 'lg':
        return 'h-5 w-5';
      case 'xl':
        return 'h-6 w-6';
      default:
        return 'h-2.5 w-2.5';
    }
  };

  return (
    <div className="relative">
      <button
        onClick={showBackupCancel ? handleCancel : handleBackupClick}
        disabled={disabled || !isConnected || isLoading}
        className={`flex items-center space-x-1 ${
          showBackupCancel
            ? 'bg-red-600 hover:bg-red-700'
            : 'bg-orange-600 hover:bg-orange-700'
        } disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded transition-colors ${getButtonSize()}`}
      >
        {showBackupCancel ? (
          <>
            <X className={getIconSize()} />
            <span>Hủy</span>
          </>
        ) : (
          <>
            <Package className={getIconSize()} />
            <span>{isLoading ? 'Đang dự bị...' : 'Dự bị'}</span>
          </>
        )}
      </button>
      
      {/* Circular progress border overlay */}
      {showBackupCancel && (
        <div className="absolute inset-0 pointer-events-none rounded">
          <svg className="w-full h-full" style={{ transform: 'rotate(-90deg)' }}>
            <circle
              cx="50%"
              cy="50%"
              r="calc(50% - 3px)"
              fill="none"
              stroke="rgba(255, 255, 255, 0.9)"
              strokeWidth="3"
              strokeDasharray="100 100"
              strokeDashoffset={100 - progress}
              strokeLinecap="round"
              style={{
                transition: 'stroke-dashoffset 16ms linear',
                filter: 'drop-shadow(0 0 2px rgba(255, 255, 255, 0.5))'
              }}
            />
          </svg>
        </div>
      )}
    </div>
  );
};

export default BackupButton;

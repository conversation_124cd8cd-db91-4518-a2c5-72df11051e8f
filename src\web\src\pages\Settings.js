import React, { useEffect, useState } from 'react';
import { Settings as SettingsIcon, Save, RefreshCw, Bell, Volume2, Palette, Globe, Trash2, Printer, FileText, Bug, Plus } from 'lucide-react';
import { useApp } from '../contexts/AppContext';
import { useSocket } from '../contexts/SocketContext';
import ScrapingControl from '../components/ScrapingControl';
import PrintSettings from '../components/PrintSettings';
import CancelSettings from '../components/CancelSettings';
import ColorSettings from '../components/ColorSettings';
import MongoDBSettings from '../components/MongoDBSettings';
import AutoMessageSettings from '../components/AutoMessageSettings';

import toast from 'react-hot-toast';

const Settings = () => {
  const { state, actions } = useApp();
  const { clearProcessedComments, systemState } = useSocket();
  const [settings, setSettings] = useState(state.settings);
  const [isSaving, setIsSaving] = useState(false);

  // Debug comments state
  const [debugCommentCount, setDebugCommentCount] = useState(5);
  const [isAddingDebugComments, setIsAddingDebugComments] = useState(false);
  const [scraperDebugInfo, setScraperDebugInfo] = useState(null);

  // Update local settings when context settings change
  useEffect(() => {
    setSettings(state.settings);
  }, [state.settings]);

  useEffect(() => {
    actions.setCurrentPage('settings');

    // Handle sessionStorage scroll target (from sidebar navigation)
    const scrollTarget = sessionStorage.getItem('scrollToSection');
    if (scrollTarget) {
      sessionStorage.removeItem('scrollToSection');
      setTimeout(() => {
        const element = document.getElementById(scrollTarget);
        if (element) {
          const headerOffset = 80;
          const elementPosition = element.getBoundingClientRect().top;
          const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

          window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth'
          });

          // Add highlight effect
          element.style.transition = 'box-shadow 0.3s ease';
          element.style.boxShadow = '0 0 0 3px rgba(147, 51, 234, 0.3)';
          setTimeout(() => {
            element.style.boxShadow = '';
          }, 2000);
        }
      }, 100); // Small delay to ensure page is rendered
    }

    // Handle URL hash for direct navigation (if someone manually types #section)
    const hash = window.location.hash.substring(1);
    if (hash && !scrollTarget) {
      setTimeout(() => {
        const element = document.getElementById(hash);
        if (element) {
          const headerOffset = 80;
          const elementPosition = element.getBoundingClientRect().top;
          const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

          window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth'
          });
        }
      }, 100);
    }
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const handleSettingChange = (key, value) => {
    // Validate numeric inputs - REMOVED maxCommentsInMemory limit for unlimited comments
    if (typeof value === 'number') {
      if (key === 'refreshInterval' && (value < 1000 || value > 60000)) {
        toast.error('Thời gian làm mới phải từ 1-60 giây');
        return;
      }
      // REMOVED: maxCommentsInMemory validation - unlimited comments support

      if (key === 'messageRetryAttempts' && (value < 1 || value > 10)) {
        toast.error('Số lần thử lại phải từ 1-10');
        return;
      }
    }

    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      // Update settings in context (which automatically saves to localStorage)
      actions.updateSettings(settings);

      // Simulate a small delay for better UX
      await new Promise(resolve => setTimeout(resolve, 500));

      toast.success('Cài đặt đã được lưu thành công');
    } catch (error) {
      toast.error('Lỗi khi lưu cài đặt');
    } finally {
      setIsSaving(false);
    }
  };

  const handleReset = () => {
    setSettings(state.settings);
    toast.info('Đã khôi phục cài đặt');
  };

  const handleClearProcessedComments = () => {
    if (window.confirm('Bạn có chắc muốn xóa bộ nhớ comment? Tất cả comment sẽ được hiển thị lại như mới.')) {
      clearProcessedComments();
    }
  };

  const handleClearAllData = async () => {
    if (window.confirm('⚠️ CẢNH BÁO: Bạn có chắc muốn xóa TẤT CẢ dữ liệu? Hành động này KHÔNG THỂ hoàn tác!')) {
      if (window.confirm('Xác nhận lần cuối: Xóa vĩnh viễn tất cả bình luận, đơn hàng và cài đặt?')) {
        try {
          // Clear localStorage
          localStorage.clear();
          // Clear sessionStorage
          sessionStorage.clear();
          toast.success('Đã xóa tất cả dữ liệu thành công');
          // Reload page to reset state
          setTimeout(() => window.location.reload(), 1000);
        } catch (error) {
          toast.error('Lỗi khi xóa dữ liệu');
        }
      }
    }
  };

  const handleResetSettings = () => {
    if (window.confirm('Bạn có chắc muốn khôi phục tất cả cài đặt về mặc định?')) {
      const defaultSettings = {
        autoRefresh: true,
        refreshInterval: 5000,
        notificationsEnabled: true,
        soundEnabled: true,
        theme: 'light',
        language: 'vi',
        maxCommentsInMemory: 1000,
        messageRetryAttempts: 3
      };
      setSettings(defaultSettings);
      actions.updateSettings(defaultSettings);
      toast.success('Đã khôi phục cài đặt về mặc định');
    }
  };

  const fetchScraperDebugInfo = async () => {
    try {
      const response = await fetch('/api/scraper-status');
      const data = await response.json();
      setScraperDebugInfo(data);
    } catch (error) {
      console.error('Error fetching scraper debug info:', error);
      setScraperDebugInfo({ error: error.message });
    }
  };

  const handleAddDebugComments = async () => {
    if (debugCommentCount < 1 || debugCommentCount > 100) {
      toast.error('Số lượng comment phải từ 1 đến 100');
      return;
    }

    setIsAddingDebugComments(true);
    try {
      const response = await fetch('/api/add-debug-comments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ count: debugCommentCount }),
      });

      const data = await response.json();

      if (data.success) {
        toast.success(`✅ Đã thêm ${debugCommentCount} comment debug thành công!`);
        console.log('Debug comments added:', data);
      } else {
        toast.error(data.error || 'Lỗi khi thêm debug comments');
        console.error('Debug comments error:', data);
      }
    } catch (error) {
      console.error('Error adding debug comments:', error);
      toast.error('Lỗi kết nối khi thêm debug comments');
    } finally {
      setIsAddingDebugComments(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Cài đặt</h1>
          <p className="mt-1 text-sm text-gray-500">
            Tùy chỉnh hệ thống theo nhu cầu của bạn
          </p>
        </div>

        <div className="mt-4 sm:mt-0 flex space-x-3">
          <button
            onClick={handleReset}
            className="btn-secondary"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Khôi phục
          </button>
          <button
            onClick={handleSave}
            disabled={isSaving}
            className="btn-primary"
          >
            {isSaving ? (
              <div className="spinner mr-2" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            Lưu cài đặt
          </button>
        </div>
      </div>

      {/* Settings Sections */}
      <div className="space-y-6">
        {/* Scraping Control */}
        <div id="scraping">
          <ScrapingControl />
        </div>

        {/* Print Settings - Unified */}
        <div id="printer">
          <PrintSettings />
        </div>

        {/* Cancel Settings */}
        <div id="cancel-settings">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <SettingsIcon className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <h2 className="text-lg font-semibold text-gray-900">
                    Cài đặt nút hủy
                  </h2>
                  <p className="text-sm text-gray-500">
                    Tùy chỉnh tính năng hủy cho nút in và dự bị
                  </p>
                </div>
              </div>
              <CancelSettings />
            </div>
          </div>
        </div>

        {/* Color Settings */}
        <div id="colors">
          <ColorSettings />
        </div>

        {/* MongoDB Settings */}
        <div id="mongodb">
          <MongoDBSettings />
        </div>

        {/* Auto Message Settings */}
        <div id="auto-message">
          <AutoMessageSettings />
        </div>

        {/* Debug Comments */}
        <div id="debug-comments" className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center mb-4">
            <div className="p-2 bg-blue-100 rounded-lg mr-3">
              <Bug className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Debug Comments</h3>
              <p className="text-sm text-gray-500">Thêm comment giả để test hệ thống</p>
            </div>
          </div>

          <div className="space-y-4">
            <div className="p-4 bg-blue-50 rounded-lg">
              <div className="flex items-center space-x-4">
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Số lượng comment debug
                  </label>
                  <input
                    type="number"
                    min="1"
                    max="100"
                    value={debugCommentCount}
                    onChange={(e) => setDebugCommentCount(parseInt(e.target.value) || 1)}
                    className="input w-32"
                    placeholder="1-100"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Nhập số lượng comment giả muốn thêm (1-100)
                  </p>
                </div>
                <div className="flex-shrink-0">
                  <button
                    onClick={handleAddDebugComments}
                    disabled={isAddingDebugComments || !systemState?.isRunning}
                    className="btn-primary flex items-center"
                  >
                    {isAddingDebugComments ? (
                      <div className="spinner mr-2" />
                    ) : (
                      <Plus className="h-4 w-4 mr-2" />
                    )}
                    Thêm Comments
                  </button>
                </div>
              </div>
            </div>

            <div className="text-sm text-gray-600 space-y-2">
              <div className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                <p><strong>Cách hoạt động:</strong> Inject comments vào DOM của trang Instagram Live</p>
              </div>
              <div className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <p><strong>Test thực tế:</strong> Scraper sẽ detect như comments thật từ DOM</p>
              </div>
              <div className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0"></div>
                <p><strong>Pipeline đầy đủ:</strong> DOM → Scraper → Comments page → Print/Messaging</p>
              </div>
              <div className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2 flex-shrink-0"></div>
                <p><strong>Nhận dạng:</strong> Debug comments có avatar màu gradient và username test_*</p>
              </div>
            </div>

            {!systemState?.isRunning && (
              <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-4 h-4 bg-yellow-400 rounded-full mr-2"></div>
                    <p className="text-sm text-yellow-800">
                      <strong>Scraper chưa chạy:</strong> Cần khởi động scraper trước khi thêm debug comments
                    </p>
                  </div>
                  <button
                    onClick={fetchScraperDebugInfo}
                    className="text-xs bg-blue-100 hover:bg-blue-200 text-blue-700 px-2 py-1 rounded"
                  >
                    Debug Info
                  </button>
                </div>
              </div>
            )}

            {scraperDebugInfo && (
              <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-sm font-medium text-gray-700">Scraper Debug Info</h4>
                  <button
                    onClick={() => setScraperDebugInfo(null)}
                    className="text-xs text-gray-500 hover:text-gray-700"
                  >
                    ✕
                  </button>
                </div>
                <pre className="text-xs text-gray-600 bg-white p-2 rounded border overflow-auto max-h-40">
                  {JSON.stringify(scraperDebugInfo, null, 2)}
                </pre>
              </div>
            )}
          </div>
        </div>

        {/* General Settings */}
        <div id="general" className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center mb-4">
            <SettingsIcon className="h-5 w-5 text-gray-600 mr-2" />
            <h3 className="text-lg font-semibold text-gray-900">Cài đặt chung</h3>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">Tự động làm mới</label>
                <p className="text-xs text-gray-500">Tự động cập nhật dữ liệu mới</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.autoRefresh}
                  onChange={(e) => handleSettingChange('autoRefresh', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
              </label>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Thời gian làm mới (giây)
              </label>
              <input
                type="number"
                min="1"
                max="60"
                value={settings.refreshInterval / 1000}
                onChange={(e) => handleSettingChange('refreshInterval', parseInt(e.target.value) * 1000)}
                className="input w-32"
                disabled={!settings.autoRefresh}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Ngôn ngữ</label>
              <select
                value={settings.language}
                onChange={(e) => handleSettingChange('language', e.target.value)}
                className="select w-48"
              >
                <option value="vi">Tiếng Việt</option>
                <option value="en">English</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Giao diện</label>
              <select
                value={settings.theme}
                onChange={(e) => handleSettingChange('theme', e.target.value)}
                className="select w-48"
              >
                <option value="light">Sáng</option>
                <option value="dark">Tối</option>
                <option value="auto">Tự động</option>
              </select>
            </div>
          </div>
        </div>

        {/* Notification Settings */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center mb-4">
            <Bell className="h-5 w-5 text-gray-600 mr-2" />
            <h3 className="text-lg font-semibold text-gray-900">Thông báo</h3>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">Bật thông báo</label>
                <p className="text-xs text-gray-500">Nhận thông báo khi có bình luận mới</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.notificationsEnabled}
                  onChange={(e) => handleSettingChange('notificationsEnabled', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">Âm thanh thông báo</label>
                <p className="text-xs text-gray-500">Phát âm thanh khi có thông báo</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.soundEnabled}
                  onChange={(e) => handleSettingChange('soundEnabled', e.target.checked)}
                  className="sr-only peer"
                  disabled={!settings.notificationsEnabled}
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600 disabled:opacity-50"></div>
              </label>
            </div>
          </div>
        </div>





        {/* Advanced Settings */}
        <div id="advanced" className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Cài đặt nâng cao</h3>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Số lượng bình luận tối đa trong bộ nhớ (Không giới hạn)
              </label>
              <input
                type="number"
                min="1000"
                value={settings.maxCommentsInMemory || 100000}
                onChange={(e) => handleSettingChange('maxCommentsInMemory', parseInt(e.target.value))}
                className="input w-32"
              />
              <p className="text-xs text-gray-500 mt-1">
                Hệ thống có thể xử lý 100,000+ comment mà không lag
              </p>
            </div>



            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Số lần thử lại khi gửi tin nhắn thất bại
              </label>
              <input
                type="number"
                min="1"
                max="10"
                value={settings.messageRetryAttempts || 3}
                onChange={(e) => handleSettingChange('messageRetryAttempts', parseInt(e.target.value))}
                className="input w-32"
              />
            </div>
          </div>
        </div>

        {/* Danger Zone */}
        <div className="bg-white rounded-lg shadow-sm border border-red-200 p-6">
          <h3 className="text-lg font-semibold text-red-900 mb-4">Vùng nguy hiểm</h3>

          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-red-50 rounded-lg">
              <div>
                <h4 className="font-medium text-red-900">Xóa tất cả dữ liệu</h4>
                <p className="text-sm text-red-700">Xóa vĩnh viễn tất cả bình luận, đơn hàng và cài đặt</p>
              </div>
              <button
                onClick={handleClearAllData}
                className="btn-danger"
              >
                Xóa dữ liệu
              </button>
            </div>

            <div className="flex items-center justify-between p-4 bg-red-50 rounded-lg">
              <div>
                <h4 className="font-medium text-red-900">Khôi phục cài đặt gốc</h4>
                <p className="text-sm text-red-700">Đặt lại tất cả cài đặt về mặc định</p>
              </div>
              <button
                onClick={handleResetSettings}
                className="btn-danger"
              >
                Khôi phục
              </button>
            </div>

            <div className="flex items-center justify-between p-4 bg-yellow-50 rounded-lg">
              <div>
                <h4 className="font-medium text-yellow-900">Xóa bộ nhớ comment</h4>
                <p className="text-sm text-yellow-700">Xóa danh sách comment đã xử lý - tất cả comment sẽ hiển thị lại như mới</p>
              </div>
              <button
                onClick={handleClearProcessedComments}
                className="btn-warning flex items-center"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Xóa bộ nhớ
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings;

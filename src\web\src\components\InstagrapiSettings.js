import React, { useState, useEffect } from 'react';
import { useSocket } from '../contexts/SocketContext';
import { showToast } from '../utils/toastManager';
import { LogOut } from 'lucide-react';
import credentialsService from '../services/credentialsService';
import useAutoLogin from '../hooks/useAutoLogin';
import { getApiUrl } from '../config/api';

const InstagrapiSettings = () => {
    const [username, setUsername] = useState('');
    const [password, setPassword] = useState('');
    const [sessionId, setSessionId] = useState('');
    const [useSessionId, setUseSessionId] = useState(false);
    const [status, setStatus] = useState('idle');
    const [isRunning, setIsRunning] = useState(false);
    const [serviceStatus, setServiceStatus] = useState({
        processRunning: false,
        serviceResponding: false,
        processId: null
    });
    const [saveCredentials, setSaveCredentials] = useState(false);
    const [hasSavedCredentials, setHasSavedCredentials] = useState(false);
    const [savedUsername, setSavedUsername] = useState('');
    const socket = useSocket();

    // Auto login functionality
    const autoLoginFunction = async (username, password, isAutoLogin = false) => {
        if (isAutoLogin) {
            setStatus('connecting');
            showToast('Đang tự động đăng nhập...', 'info');
        }

        try {
            const response = await fetch(getApiUrl('/api/messenger/instagrapi/login'), {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    username,
                    password,
                    saveCredentials: true
                })
            });

            const data = await response.json();
            if (data.success) {
                setStatus('connected');
                if (isAutoLogin) {
                    showToast('Tự động đăng nhập thành công', 'success');
                }
                await startQueueProcessor();
                return true;
            } else {
                throw new Error(data.message || 'Login failed');
            }
        } catch (error) {
            setStatus('error');
            if (isAutoLogin) {
                showToast('Tự động đăng nhập thất bại', 'error');
            }
            throw error;
        }
    };

    const { isAutoLogging } = useAutoLogin('instagrapi', autoLoginFunction);

    useEffect(() => {
        checkStatus();
        checkServiceStatus();
        checkSavedCredentials();

        // Kiểm tra status mỗi 30 giây
        const interval = setInterval(() => {
            checkStatus();
            checkServiceStatus();
        }, 30000);

        return () => clearInterval(interval);
    }, []); // eslint-disable-line react-hooks/exhaustive-deps

    const checkSavedCredentials = async () => {
        try {
            // Check server API first (for cross-device compatibility)
            const response = await fetch(getApiUrl('/api/instagrapi/credentials'));
            const data = await response.json();

            if (data.success && data.hasCredentials) {
                setHasSavedCredentials(true);
                setSavedUsername(data.username || '(Đã lưu)');
                console.log('🔐 Found saved Instagrapi credentials on server:', data.username);
                return;
            }

            // Fallback to local storage check
            const saved = credentialsService.getSavedCredentials('instagrapi');
            if (saved) {
                setHasSavedCredentials(true);
                setSavedUsername(saved.username);
                setUsername(saved.username);
                setPassword(saved.password);
                console.log('🔐 Found saved Instagrapi credentials in localStorage:', saved.username);
            } else {
                setHasSavedCredentials(false);
                setSavedUsername('');
                console.log('🔐 No saved Instagrapi credentials found');
            }
        } catch (error) {
            console.error('Error checking saved credentials:', error);
            // Fallback to local storage on error
            const saved = credentialsService.getSavedCredentials('instagrapi');
            if (saved) {
                setHasSavedCredentials(true);
                setSavedUsername(saved.username);
                setUsername(saved.username);
                setPassword(saved.password);
            }
        }
    };

    const handleLogout = () => {
        credentialsService.clearSavedCredentials('instagrapi');
        setUsername('');
        setPassword('');
        setHasSavedCredentials(false);
        setSavedUsername('');
        showToast('Đã xóa thông tin đăng nhập đã lưu', 'success');
    };

    const handleAutoLogin = async () => {
        setStatus('connecting');

        try {
            const response = await fetch(getApiUrl('/api/messenger/instagrapi/auto-login'), {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });

            const data = await response.json();

            if (data.success) {
                setStatus('connected');
                showToast('Đăng nhập tự động thành công', 'success');

                // Bắt đầu queue processor sau khi đăng nhập thành công
                await startQueueProcessor();
            } else {
                setStatus('error');
                showToast(`Đăng nhập tự động thất bại: ${data.message}`, 'error');
            }
        } catch (error) {
            console.error('Auto login error:', error);
            setStatus('error');
            showToast('Lỗi đăng nhập tự động: ' + error.message, 'error');
        }
    };

    const handleClearCredentials = async () => {
        try {
            const response = await fetch(getApiUrl('/api/instagrapi/credentials'), {
                method: 'DELETE'
            });

            const data = await response.json();
            if (data.success) {
                setHasSavedCredentials(false);
                showToast('Đã xóa thông tin đăng nhập đã lưu', 'success');
            }
        } catch (error) {
            console.error('Error clearing credentials:', error);
            showToast('Lỗi khi xóa thông tin đăng nhập', 'error');
        }
    };

    const checkStatus = async () => {
        try {
            const response = await fetch(getApiUrl('/api/messenger/instagrapi/status'));
            const data = await response.json();

            if (data.success) {
                setIsRunning(data.status.isRunning);
                setStatus(data.status.isLoggedIn ? 'connected' : 'idle');
            }
        } catch (error) {
            console.error('Failed to check instagrapi status:', error);
        }
    };

    const checkServiceStatus = async () => {
        try {
            const response = await fetch(getApiUrl('/api/messenger/instagrapi/service-status'));
            const data = await response.json();

            if (data.success) {
                setServiceStatus({
                    processRunning: data.processRunning,
                    serviceResponding: data.serviceResponding,
                    processId: data.processId
                });
            }
        } catch (error) {
            console.error('Failed to check service status:', error);
            setServiceStatus({
                processRunning: false,
                serviceResponding: false,
                processId: null
            });
        }
    };

    const handleStartService = async () => {
        try {
            showToast('Đang khởi động InstagrapiService...', 'info');
            const response = await fetch(getApiUrl('/api/messenger/instagrapi/start-service'), {
                method: 'POST'
            });
            const data = await response.json();

            if (data.success) {
                showToast('InstagrapiService đã được khởi động thành công', 'success');
                // Kiểm tra lại status sau 3 giây
                setTimeout(() => {
                    checkServiceStatus();
                    checkStatus();
                }, 3000);
            } else {
                showToast(`Lỗi khởi động service: ${data.message}`, 'error');
            }
        } catch (error) {
            console.error('Failed to start service:', error);
            showToast('Lỗi khi khởi động InstagrapiService', 'error');
        }
    };

    const handleLogin = async () => {
        setStatus('connecting');

        try {
            let response;

            if (useSessionId) {
                if (!sessionId) {
                    showToast('Vui lòng nhập Session ID', 'error');
                    setStatus('idle');
                    return;
                }

                response = await fetch(getApiUrl('/api/messenger/instagrapi/login-by-session'), {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ sessionId })
                });
            } else {
                // Use saved credentials if available, otherwise use form input
                let credentialsToUse = { username, password };

                if (hasSavedCredentials) {
                    const saved = credentialsService.getSavedCredentials('instagrapi');
                    if (saved) {
                        credentialsToUse = { username: saved.username, password: saved.password };
                    }
                } else {
                    if (!username || !password) {
                        showToast('Vui lòng nhập đầy đủ tên đăng nhập và mật khẩu', 'error');
                        setStatus('idle');
                        return;
                    }
                }

                response = await fetch(getApiUrl('/api/messenger/instagrapi/login'), {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        username: credentialsToUse.username,
                        password: credentialsToUse.password,
                        saveCredentials: hasSavedCredentials ? true : saveCredentials
                    })
                });
            }

            const data = await response.json();

            if (data.success) {
                setStatus('connected');
                showToast('Đăng nhập Instagram thành công', 'success');

                if (!hasSavedCredentials && saveCredentials && !useSessionId) {
                    // Save credentials to local storage
                    credentialsService.saveCredentials(username, password, 'instagrapi');
                    setHasSavedCredentials(true);
                    setSavedUsername(username);
                }

                // Bắt đầu queue processor sau khi đăng nhập thành công
                await startQueueProcessor();
            } else {
                setStatus('error');
                showToast(`Đăng nhập thất bại: ${data.message}`, 'error');
            }
        } catch (error) {
            console.error('Login error:', error);
            setStatus('error');
            showToast(`Lỗi đăng nhập: ${error.message}`, 'error');
        }
    };

    const startQueueProcessor = async () => {
        try {
            const response = await fetch(getApiUrl('/api/messenger/instagrapi/start'), {
                method: 'POST'
            });

            const data = await response.json();

            if (data.success) {
                setIsRunning(true);
                showToast('Bắt đầu xử lý hàng đợi tin nhắn', 'success');
            } else {
                showToast(`Không thể bắt đầu xử lý: ${data.message}`, 'error');
            }
        } catch (error) {
            console.error('Start queue processor error:', error);
            showToast(`Lỗi bắt đầu xử lý: ${error.message}`, 'error');
        }
    };

    const stopQueueProcessor = async () => {
        try {
            const response = await fetch(getApiUrl('/api/messenger/instagrapi/stop'), {
                method: 'POST'
            });

            const data = await response.json();

            if (data.success) {
                setIsRunning(false);
                showToast('Đã dừng xử lý hàng đợi tin nhắn', 'success');
            } else {
                showToast(`Không thể dừng xử lý: ${data.message}`, 'error');
            }
        } catch (error) {
            console.error('Stop queue processor error:', error);
            showToast(`Lỗi dừng xử lý: ${error.message}`, 'error');
        }
    };

    return (
        <div className="bg-white p-4 rounded-lg shadow mb-4">
            <h2 className="text-lg font-bold mb-4">Cài đặt Instagrapi API</h2>

            {/* Service Status */}
            <div className="mb-4 p-3 bg-gray-50 rounded-lg">
                <h3 className="text-sm font-semibold text-gray-700 mb-2">Trạng thái InstagrapiService</h3>
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-2">
                            <div className={`w-3 h-3 rounded-full ${serviceStatus.serviceResponding ? 'bg-green-500' : 'bg-red-500'}`}></div>
                            <span className="text-sm">
                                {serviceStatus.serviceResponding ? 'Service đang chạy' : 'Service không phản hồi'}
                            </span>
                        </div>
                        {serviceStatus.processId && (
                            <span className="text-xs text-gray-500">PID: {serviceStatus.processId}</span>
                        )}
                    </div>
                    {!serviceStatus.serviceResponding && (
                        <button
                            onClick={handleStartService}
                            className="px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600"
                        >
                            Khởi động Service
                        </button>
                    )}
                </div>
            </div>

            <div className="mb-4">
                <label className="flex items-center space-x-2 mb-2">
                    <input
                        type="checkbox"
                        className="h-4 w-4 text-blue-600"
                        checked={useSessionId}
                        onChange={() => setUseSessionId(!useSessionId)}
                        disabled={status === 'connected' || status === 'connecting'}
                    />
                    <span className="text-sm">Sử dụng Session ID thay vì đăng nhập</span>
                </label>
            </div>
            {!useSessionId ? (
                <>
                    {/* Saved credentials info */}
                    {hasSavedCredentials && (
                        <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-green-800">
                                        {isAutoLogging ? 'Đang tự động đăng nhập...' : 'Đã lưu thông tin đăng nhập'}
                                    </p>
                                    <p className="text-xs text-green-600">@{savedUsername}</p>
                                </div>
                                <button
                                    type="button"
                                    onClick={handleLogout}
                                    className="flex items-center px-2 py-1 text-xs text-red-600 hover:text-red-800 hover:bg-red-50 rounded transition-colors"
                                    disabled={status === 'connected' || status === 'connecting'}
                                >
                                    <LogOut className="h-3 w-3 mr-1" />
                                    Đăng xuất
                                </button>
                            </div>
                        </div>
                    )}

                    {/* Only show input fields if no saved credentials */}
                    {!hasSavedCredentials && (
                        <>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Tên đăng nhập Instagram
                                    </label>
                                    <input
                                        type="text"
                                        className="border rounded w-full p-2"
                                        placeholder="Nhập tên đăng nhập"
                                        value={username}
                                        onChange={(e) => setUsername(e.target.value)}
                                        disabled={status === 'connected' || status === 'connecting'}
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Mật khẩu
                                    </label>
                                    <input
                                        type="password"
                                        className="border rounded w-full p-2"
                                        placeholder="Nhập mật khẩu"
                                        value={password}
                                        onChange={(e) => setPassword(e.target.value)}
                                        disabled={status === 'connected' || status === 'connecting'}
                                    />
                                </div>
                            </div>
                            <div className="mb-4">
                                <label className="flex items-center space-x-2">
                                    <input
                                        type="checkbox"
                                        className="h-4 w-4 text-blue-600"
                                        checked={saveCredentials}
                                        onChange={(e) => setSaveCredentials(e.target.checked)}
                                        disabled={status === 'connected' || status === 'connecting'}
                                    />
                                    <span className="text-sm">Lưu thông tin đăng nhập để tự động đăng nhập lần sau</span>
                                </label>
                            </div>
                        </>
                    )}
                </>
            ) : (
                <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                        Instagram Session ID
                    </label>
                    <input
                        type="text"
                        className="border rounded w-full p-2"
                        placeholder="Nhập Session ID từ Instagram"
                        value={sessionId}
                        onChange={(e) => setSessionId(e.target.value)}
                        disabled={status === 'connected' || status === 'connecting'}
                    />
                </div>
            )}

            <div className="flex space-x-3 mb-3">
                {status !== 'connected' ? (
                    <button
                        onClick={hasSavedCredentials && !useSessionId ? handleAutoLogin : handleLogin}
                        disabled={status === 'connecting'}
                        className={`px-4 py-2 rounded ${status === 'connecting' ? 'bg-yellow-500' : 'bg-green-500'
                            } text-white`}
                    >
                        {status === 'connecting'
                            ? 'Đang kết nối...'
                            : 'Bắt đầu'
                        }
                    </button>
                ) : (
                    <div className="flex space-x-2">
                        {isRunning ? (
                            <button
                                onClick={stopQueueProcessor}
                                className="px-4 py-2 rounded bg-red-500 text-white"
                            >
                                Dừng xử lý
                            </button>
                        ) : (
                            <button
                                onClick={startQueueProcessor}
                                className="px-4 py-2 rounded bg-green-500 text-white"
                            >
                                Bắt đầu xử lý
                            </button>
                        )}
                    </div>
                )}
            </div>

            {/* Nút xóa thông tin đăng nhập đã lưu */}
            {hasSavedCredentials && (
                <div className="mb-3">
                    <button
                        onClick={handleClearCredentials}
                        className="px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600"
                    >
                        Xóa thông tin đăng nhập đã lưu
                    </button>
                </div>
            )}

            <div className="mt-3">
                <div className="flex items-center space-x-2">
                    <div className={`w-3 h-3 rounded-full ${status === 'idle' ? 'bg-gray-400' :
                        status === 'connecting' ? 'bg-yellow-500' :
                            status === 'connected' ? 'bg-green-500' :
                                'bg-red-500'
                        }`}></div>
                    <span className="text-sm text-gray-600">
                        {status === 'idle' ? 'Chưa kết nối' :
                            status === 'connecting' ? 'Đang kết nối...' :
                                status === 'connected' ? 'Đã kết nối' :
                                    'Lỗi kết nối'}
                    </span>
                </div>
                <div className="flex items-center space-x-2 mt-1">
                    <div className={`w-3 h-3 rounded-full ${isRunning ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                    <span className="text-sm text-gray-600">
                        {isRunning ? 'Đang xử lý hàng đợi' : 'Chưa xử lý hàng đợi'}
                        {isRunning && ' (10 giây/tin nhắn)'}
                    </span>
                </div>
            </div>
        </div>
    );
};

export default InstagrapiSettings; 
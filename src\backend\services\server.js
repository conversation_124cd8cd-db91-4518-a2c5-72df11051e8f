const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const winston = require('winston');
const fs = require('fs');
const path = require('path');
const http = require('http');
const socketIo = require('socket.io');
const { v4: uuidv4 } = require('uuid');
const Database = require('./services/Database');
const InstagramScraper = require('./services/InstagramScraper');
const InstagramMessenger = require('./services/InstagramMessenger');
const MessageQueue = require('./services/MessageQueue');
const MongoDBService = require('./services/MongoDBService');
const PrinterService = require('./services/PrinterService');

// Create winston logger
const logger = winston.createLogger({
    level: 'info',
    format: winston.format.simple(),
    transports: [
        new winston.transports.Console(),
        new winston.transports.File({ filename: path.join(__dirname, '../../logs/server.log') })
    ]
});

// Check if logs directory exists, create if not
const logsDir = path.join(__dirname, '../../logs');
if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir);
}

// Create Express app
const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
    cors: {
        origin: "*",
        methods: ["GET", "POST"]
    }
});

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Global services
const database = new Database();
global.database = database; // Make database available globally for services
global.mongoDBService = null;

// Create service instances
let instagramScraper;
let instagramMessenger;
let messageQueue;
let printerService;

// Initialize database and services
async function initializeServices() {
    try {
        logger.info('Initializing services...');

        // Initialize database first
        await database.initialize();

        // Initialize message queue service
        messageQueue = new MessageQueue(database);
        global.messageQueue = messageQueue;

        // Initialize printer service
        printerService = new PrinterService(database);
        global.printerService = printerService;

        // Initialize messenger service
        instagramMessenger = new InstagramMessenger(database);
        global.instagramMessenger = instagramMessenger;

        // Initialize scraper service
        instagramScraper = new InstagramScraper(database);
        global.instagramScraper = instagramScraper;
        // Initialize MongoDB service (if configured)
        await initializeMongoDB();

        logger.info('All services initialized successfully');
    } catch (error) {
        logger.error('Error initializing services:', error);
    }
}

// Initialize MongoDB if configured
async function initializeMongoDB() {
    try {
        const mongoSettings = await database.getSetting('mongodb_settings');
        if (mongoSettings && mongoSettings.value) {
            const settings = JSON.parse(mongoSettings.value);
            if (settings.enabled && settings.uri) {
                logger.info('Initializing MongoDB connection...');
                global.mongoDBService = new MongoDBService(settings.uri, settings.database);
                await global.mongoDBService.connect();
                logger.info('MongoDB connection established successfully');

                // Set up MongoDB sync interval
                if (settings.autoSyncInterval > 0) {
                    setInterval(async () => {
                        if (global.mongoDBService.isConnected) {
                            await global.mongoDBService.syncAllCollections();
                        }
                    }, settings.autoSyncInterval * 1000);
                    logger.info(`MongoDB auto-sync interval set to ${settings.autoSyncInterval} seconds`);
                }
            }
        }
    } catch (error) {
        logger.error('Failed to initialize MongoDB:', error);
        global.mongoDBService = null;
    }
}

// Socket.io connection handling
io.on('connection', (socket) => {
    logger.info('New client connected');

    socket.on('disconnect', () => {
        logger.info('Client disconnected');
    });

    // Setup event listeners for services
    if (instagramScraper) {
        instagramScraper.on('newComment', (comment) => {
            io.emit('newComment', comment);
        });

        instagramScraper.on('scraper-status', (status) => {
            io.emit('scraper-status', status);
        });

        instagramScraper.on('login-status', (status) => {
            io.emit('login-status', status);
        });
    }

    if (instagramMessenger) {
        instagramMessenger.on('messageStatusUpdate', (message) => {
            io.emit('messageStatusUpdate', message);
        });

        instagramMessenger.on('messaging-status', (status) => {
            io.emit('messaging-status', status);
        });

        instagramMessenger.on('messenger-connected', () => {
            io.emit('messenger-connected');
        });

        instagramMessenger.on('messenger-disconnected', () => {
            io.emit('messenger-disconnected');
        });

        instagramMessenger.on('messaging-start', (data) => {
            io.emit('messaging-start', data);
        });

        instagramMessenger.on('messaging-end', (data) => {
            io.emit('messaging-end', data);
        });
    }

    if (messageQueue) {
        messageQueue.on('queue-updated', (queueStatus) => {
            io.emit('queue-updated', queueStatus);
        });
    }

    if (printerService) {
        printerService.on('printer-status', (status) => {
            io.emit('printer-status', status);
        });
    }

    if (global.mongoDBService) {
        global.mongoDBService.on('sync-status', (status) => {
            io.emit('sync-status', status);
        });
    }
});

// API Routes

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({ status: 'ok' });
});

// Get all comments
app.get('/api/comments', async (req, res) => {
    try {
        // Using API direct mode - all comments fetched from session memory
        let comments = global.instagramScraper ? global.instagramScraper.getComments() : [];

        // Sắp xếp các bình luận theo thời gian tăng dần (cũ nhất lên đầu)
        comments = comments.sort((a, b) => {
            const timeA = new Date(a.timestamp).getTime();
            const timeB = new Date(b.timestamp).getTime();
            return timeA - timeB;
        });

        res.json(comments);
    } catch (error) {
        logger.error('Error fetching comments:', error);
        res.status(500).json({ error: error.message });
    }
});

// Start/Stop Instagram scraper
app.post('/api/scraper/start', async (req, res) => {
    try {
        if (instagramScraper) {
            // Save cookies for future use if provided
            let cookies = null;
            if (req.body.cookies) {
                cookies = req.body.cookies;
                await database.saveCookies(req.body.username, cookies, 'scraper');
            }

            const result = await instagramScraper.start(req.body, cookies);
            res.json(result);
        } else {
            res.status(500).json({ error: 'Scraper service not available' });
        }
    } catch (error) {
        logger.error('Error starting scraper:', error);
        res.status(500).json({ error: error.message });
    }
});

app.post('/api/scraper/stop', async (req, res) => {
    try {
        if (instagramScraper) {
            const result = await instagramScraper.stop();
            res.json(result);
        } else {
            res.status(500).json({ error: 'Scraper service not available' });
        }
    } catch (error) {
        logger.error('Error stopping scraper:', error);
        res.status(500).json({ error: error.message });
    }
});

// Start/Stop Instagram messenger
app.post('/api/messenger/start', async (req, res) => {
    try {
        if (instagramMessenger) {
            // Save cookies for future use if provided
            let cookies = null;
            if (req.body.cookies) {
                cookies = req.body.cookies;
                await database.saveMessengerCookies(req.body.username, cookies);
            }

            const result = await instagramMessenger.start(req.body, cookies);
            res.json(result);
        } else {
            res.status(500).json({ error: 'Messenger service not available' });
        }
    } catch (error) {
        logger.error('Error starting messenger:', error);
        res.status(500).json({ error: error.message });
    }
});

app.post('/api/messenger/stop', async (req, res) => {
    try {
        if (instagramMessenger) {
            const result = await instagramMessenger.stop();
            res.json(result);
        } else {
            res.status(500).json({ error: 'Messenger service not available' });
        }
    } catch (error) {
        logger.error('Error stopping messenger:', error);
        res.status(500).json({ error: error.message });
    }
});

// Print comment
app.post('/api/print', async (req, res) => {
    try {
        if (printerService && database) {
            const { commentPk, username, commentText, printType } = req.body;

            // Mark comment as printed in database
            await database.markCommentAsPrinted(commentPk, username, commentText, printType);

            // Queue auto message if messenger is running
            if (instagramMessenger && instagramMessenger.isRunning) {
                await messageQueue.addToQueue({
                    id: uuidv4(),
                    comment_id: commentPk,
                    username: username,
                    original_comment: commentText,
                    customer_type: await database.isRegularCustomer(username) ? 'regular' : 'new',
                    template_name: printType === 'backup' ? 'backup_notification' : 'print_notification',
                    template_type: printType === 'backup' ? 'backup' : 'normal'
                });
            }

            // Print the comment
            const result = await printerService.printComment(req.body);

            res.json(result);
        } else {
            res.status(500).json({ error: 'Print service not available' });
        }
    } catch (error) {
        logger.error('Error printing comment:', error);
        res.status(500).json({ error: error.message });
    }
});

// Start the server
const PORT = process.env.PORT || 3001;
server.listen(PORT, async () => {
    logger.info(`Server running on port ${PORT}`);
    await initializeServices();
});

// Graceful shutdown
process.on('SIGINT', async () => {
    logger.info('Shutting down server...');

    try {
        // Stop all services
        if (instagramScraper) await instagramScraper.stop();
        if (instagramMessenger) await instagramMessenger.stop();
        if (database) await database.close();
        if (global.mongoDBService) await global.mongoDBService.close();

        logger.info('All services stopped successfully');
    } catch (error) {
        logger.error('Error during shutdown:', error);
    }

    process.exit(0);
}); 
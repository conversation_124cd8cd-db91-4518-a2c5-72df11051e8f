@echo off
echo ================================================
echo Starting Test Server for Message Queue API
echo ================================================
cd /d "%~dp0"

echo Stopping any running services on ports 3001, 3003 and 5000...
npx kill-port 3001 3003 5000

echo Starting test Node.js server on port 3003...
start cmd /k "node src/backend/server-fixed.js"

echo Waiting for test server to initialize (3 seconds)...
timeout /t 3 /nobreak > nul

echo Testing API endpoints...
curl http://localhost:3003/api/health
echo.
curl http://localhost:3003/api/message-queue/next
echo.

echo.
echo ================================================
echo Starting Python test service...
echo ================================================

echo Setting environment variables...
set NODE_SERVER_PORT=3003

echo Starting Python service with test configuration...
start cmd /k "python src/backend/services/InstagrapiService-test.py"

echo.
echo ================================================
echo Test environment is now running!
echo Node.js test server: http://localhost:3003
echo Python service: http://localhost:5000
echo ================================================
echo.
echo To test the connection, visit:
echo http://localhost:5000/check_node_connection
echo.
echo To start processing messages, send a POST request to:
echo http://localhost:5000/start_queue_processor
echo.
echo Press any key to exit this window...
pause > nul 
import React, { createContext, useContext, useState, useEffect } from 'react';
import { getApiUrl } from '../config/api';
import toast from 'react-hot-toast';

const CancelSettingsContext = createContext();

export const useCancelSettings = () => {
  const context = useContext(CancelSettingsContext);
  if (!context) {
    throw new Error('useCancelSettings must be used within a CancelSettingsProvider');
  }
  return context;
};

export const CancelSettingsProvider = ({ children }) => {
  const [settings, setSettings] = useState({
    enabled: true,           // Enable/disable cancel functionality
    duration: 1500,         // Duration in milliseconds (1.5 seconds default)
    enableForPrint: true,   // Enable cancel for print button
    enableForBackup: true   // Enable cancel for backup button
  });
  const [isLoaded, setIsLoaded] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Load settings from database on mount
  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      const response = await fetch(getApiUrl('/api/cancel-settings'));
      if (!response.ok) throw new Error('Failed to fetch cancel settings');

      const data = await response.json();
      if (data.success) {
        setSettings(prev => ({ ...prev, ...data.settings }));
        console.log('Cancel settings loaded from database:', data.settings);
      } else {
        throw new Error(data.error || 'Failed to fetch cancel settings');
      }
    } catch (error) {
      console.error('Failed to load cancel settings from server:', error);

      // Fallback to localStorage if server fails
      const savedSettings = localStorage.getItem('cancelSettings');
      if (savedSettings) {
        try {
          const parsed = JSON.parse(savedSettings);
          setSettings(prev => ({ ...prev, ...parsed }));
          console.log('Cancel settings loaded from local storage fallback');
        } catch (parseError) {
          console.error('Failed to parse cancel settings from localStorage:', parseError);
        }
      }
    } finally {
      setIsLoaded(true);
    }
  };

  // Save settings to database and localStorage when they change
  useEffect(() => {
    if (isLoaded && !isSaving) {
      saveSettings();
    }
  }, [settings, isLoaded]);

  const saveSettings = async () => {
    if (!isLoaded) return;

    // Always save to localStorage as backup
    localStorage.setItem('cancelSettings', JSON.stringify(settings));

    // Save to database for cross-device sync
    setIsSaving(true);
    try {
      const response = await fetch(getApiUrl('/api/cancel-settings'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ settings }),
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to save cancel settings');
      }

      console.log('Cancel settings saved to database');
    } catch (error) {
      console.error('Failed to save cancel settings to server:', error);
      // No need for user notification on background saves
    } finally {
      setIsSaving(false);
    }
  };

  const updateSettings = (newSettings) => {
    setSettings(prev => ({ ...prev, ...newSettings }));
  };

  const resetToDefaults = () => {
    const defaultSettings = {
      enabled: true,
      duration: 1500,
      enableForPrint: true,
      enableForBackup: true
    };

    setSettings(defaultSettings);
  };

  // Helper function to check if cancel should be enabled for specific action
  const isCancelEnabled = (action = 'both') => {
    if (!settings.enabled) return false;

    switch (action) {
      case 'print':
        return settings.enableForPrint;
      case 'backup':
        return settings.enableForBackup;
      case 'both':
      default:
        return settings.enableForPrint || settings.enableForBackup;
    }
  };

  const value = {
    settings,
    updateSettings,
    resetToDefaults,
    isCancelEnabled,
    refreshSettings: fetchSettings
  };

  return (
    <CancelSettingsContext.Provider value={value}>
      {children}
    </CancelSettingsContext.Provider>
  );
};

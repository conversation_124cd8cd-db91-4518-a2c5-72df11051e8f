const axios = require('axios');
const winston = require('winston');

// Khởi tạo logger
const logger = winston.createLogger({
    level: 'info',
    format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.printf(({ timestamp, level, message }) => {
            return `[${timestamp}] [${level.toUpperCase()}] [InstagrapiMessenger] ${message}`;
        })
    ),
    transports: [
        new winston.transports.Console()
    ]
});

const API_BASE_URL = 'http://localhost:5000';

class InstagrapiMessenger {
    constructor() {
        this.isLoggedIn = false;
        this.isRunning = false;
        this.checkStatusInterval = null;
    }

    async login(username, password) {
        try {
            logger.info('Đăng nhập Instagram với Instagrapi...');
            const response = await axios.post(`${API_BASE_URL}/login`, {
                username,
                password
            });
            this.isLoggedIn = response.data.status === 'success';
            if (this.isLoggedIn) {
                this.startStatusCheck();
            }
            return response.data;
        } catch (error) {
            logger.error('Instagrapi login error:', error.message);
            return { status: 'error', message: error.message };
        }
    }

    async loginBySessionId(sessionId) {
        try {
            logger.info('Đăng nhập Instagram với Session ID...');
            const response = await axios.post(`${API_BASE_URL}/login_by_session`, {
                sessionid: sessionId
            });
            this.isLoggedIn = response.data.status === 'success';
            if (this.isLoggedIn) {
                this.startStatusCheck();
            }
            return response.data;
        } catch (error) {
            logger.error('Instagrapi session login error:', error.message);
            return { status: 'error', message: error.message };
        }
    }

    async sendMessage(message, threadId, username) {
        if (!this.isLoggedIn) {
            return { status: 'error', message: 'Chưa đăng nhập Instagram' };
        }

        try {
            // Thử refresh session trước khi gửi tin nhắn
            await this.refreshSession();

            // Đơn giản hóa: Luôn gửi trực tiếp tới username
            const payload = { message, username };

            logger.info(`Gửi tin nhắn trực tiếp tới @${username}`);
            const response = await axios.post(`${API_BASE_URL}/send_message`, payload);
            return response.data;
        } catch (error) {
            logger.error('Instagrapi send message error:', error.message);
            return { status: 'error', message: error.message };
        }
    }

    async unsendMessage(threadId, messageId) {
        try {
            logger.info(`Thu hồi tin nhắn: thread_id=${threadId}, message_id=${messageId}`);
            const response = await axios.post(`${API_BASE_URL}/unsend_message`, {
                thread_id: threadId,
                message_id: messageId
            });
            return response.data;
        } catch (error) {
            logger.error('Instagrapi unsend message error:', error.message);
            return { status: 'error', message: error.message };
        }
    }

    async startQueueProcessor() {
        try {
            logger.info('Bắt đầu xử lý hàng đợi tin nhắn...');
            const response = await axios.post(`${API_BASE_URL}/start_queue_processor`);
            this.isRunning = response.data.status === 'success';
            return response.data;
        } catch (error) {
            logger.error('Failed to start queue processor:', error.message);
            return { status: 'error', message: error.message };
        }
    }

    async stopQueueProcessor() {
        try {
            logger.info('Dừng xử lý hàng đợi tin nhắn...');
            const response = await axios.post(`${API_BASE_URL}/stop_queue_processor`);
            this.isRunning = false;
            return response.data;
        } catch (error) {
            logger.error('Failed to stop queue processor:', error.message);
            return { status: 'error', message: error.message };
        }
    }

    async checkStatus() {
        try {
            const response = await axios.get(`${API_BASE_URL}/status`);
            if (response.data.status === 'success') {
                this.isLoggedIn = response.data.is_logged_in;
                this.isRunning = response.data.is_running;
            }
            return response.data;
        } catch (error) {
            logger.warn('Unable to check Instagrapi status:', error.message);
            return { status: 'error', is_logged_in: false, is_running: false };
        }
    }

    startStatusCheck() {
        if (this.checkStatusInterval) {
            clearInterval(this.checkStatusInterval);
        }
        this.checkStatusInterval = setInterval(async () => {
            await this.checkStatus();
        }, 30000); // Kiểm tra mỗi 30 giây
    }

    stopStatusCheck() {
        if (this.checkStatusInterval) {
            clearInterval(this.checkStatusInterval);
            this.checkStatusInterval = null;
        }
    }

    getStatus() {
        return {
            isLoggedIn: this.isLoggedIn,
            isRunning: this.isRunning,
            isProcessingQueue: this.isRunning // For compatibility
        };
    }

    // Thêm phương thức để refresh session nếu cần
    async refreshSession() {
        try {
            // Kiểm tra tình trạng session
            const statusResponse = await axios.get(`${API_BASE_URL}/status`, { timeout: 2000 });

            // Nếu không còn đăng nhập, thử refresh
            if (!statusResponse.data.is_logged_in) {
                logger.warn('Phát hiện session hết hạn, đang thử refresh...');

                // Gọi endpoint refresh session (cần thêm trong InstagrapiService.py)
                const refreshResponse = await axios.post(`${API_BASE_URL}/refresh_session`);

                if (refreshResponse.data.status === 'success') {
                    logger.info('Session đã được refresh thành công');
                } else {
                    logger.error('Không thể refresh session:', refreshResponse.data.message);
                    this.isLoggedIn = false;
                }
            }
        } catch (error) {
            logger.warn('Không thể kiểm tra hoặc refresh session:', error.message);
        }
    }
}

module.exports = InstagrapiMessenger; 
from flask import Flask, request, jsonify
from instagrapi import Client
import json
import os
import time
import threading
import requests
import sys
import random
from datetime import datetime

# Fix encoding issues on Windows
import locale

# Set UTF-8 encoding for stdout/stderr
if sys.platform.startswith('win'):
    # Set environment variables for UTF-8
    os.environ['PYTHONIOENCODING'] = 'utf-8'

    # Reconfigure stdout and stderr to use UTF-8
    try:
        sys.stdout.reconfigure(encoding='utf-8')
        sys.stderr.reconfigure(encoding='utf-8')
    except AttributeError:
        # For older Python versions
        import codecs
        sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
        sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

    # Set locale to UTF-8 if possible
    try:
        locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
    except locale.Error:
        try:
            locale.setlocale(locale.LC_ALL, 'C.UTF-8')
        except locale.Error:
            pass

app = Flask(__name__)
app.config['JSON_AS_ASCII'] = False  # Allow non-ASCII characters in JSON responses
cl = Client()
is_running = False

# Safe print function to handle encoding issues
def safe_print(message):
    """Print function that handles encoding issues on Windows"""
    try:
        print(message)
    except UnicodeEncodeError:
        # Fallback 1: Try to write to stdout buffer directly
        try:
            if hasattr(sys.stdout, 'buffer'):
                sys.stdout.buffer.write((message + '\n').encode('utf-8'))
                sys.stdout.buffer.flush()
            else:
                # Fallback 2: encode to ASCII with replacement characters
                print(message.encode('ascii', 'replace').decode('ascii'))
        except:
            print("[ENCODING ERROR - MESSAGE CANNOT BE DISPLAYED]")

# Cấu hình từ biến môi trường hoặc mặc định
NODE_SERVER_PORT = os.environ.get('NODE_SERVER_PORT', '3001')
SESSION_FILE = "instagram_session.json"
NODE_SERVER_URL = f"http://localhost:{NODE_SERVER_PORT}"

print(f"=== INSTAGRAPI SERVICE CONFIGURATION ===")
print(f"Node.js Server URL: {NODE_SERVER_URL}")
print(f"Session File: {SESSION_FILE}")
print(f"Python version: {sys.version}")
print(f"=======================================")

@app.route('/', methods=['GET'])
def index():
    return jsonify({
        "status": "running",
        "service": "InstagrapiService",
        "config": {
            "node_server_url": NODE_SERVER_URL,
            "session_file": SESSION_FILE,
            "is_running": is_running,
            "is_logged_in": cl.user_id is not None
        }
    })

@app.route('/login', methods=['POST'])
def login():
    data = request.json
    global cl
    try:
        print(f"=== ATTEMPTING LOGIN FOR {data.get('username', 'N/A')} ===")
        # Kiểm tra session đã lưu
        if os.path.exists(SESSION_FILE):
            print(f"Loading saved session from {SESSION_FILE}")
            cl.load_settings(SESSION_FILE)
            
            # Kiểm tra xem session còn hợp lệ không
            try:
                # Thử gọi một API yêu cầu xác thực để kiểm tra
                print("Verifying session validity...")
                cl.account_info()  # Phương thức này sẽ ném lỗi nếu session hết hạn
                print(f"Session is valid for user ID: {cl.user_id}")
                
                # Session vẫn tốt, không cần đăng nhập lại
                print("✅ Session still valid, no need to re-login")
                return jsonify({"status": "success", "message": "Dang nhap thanh cong voi session hien tai", "user_id": cl.user_id})
                
            except Exception as e:
                print(f"Session invalid or expired: {str(e)}")
                safe_print("🗑️ Xóa file session cũ vì đã hết hạn")

                # Xóa file session cũ
                try:
                    os.remove(SESSION_FILE)
                    safe_print(f"✅ Đã xóa file session cũ: {SESSION_FILE}")
                except Exception as del_error:
                    safe_print(f"⚠️ Không thể xóa file session cũ: {str(del_error)}")

                # Reset client trước khi đăng nhập lại
                cl = Client()

                # Session không hợp lệ, đăng nhập lại với thông tin đăng nhập
                try:
                    cl.login(data['username'], data['password'])
                    safe_print("✅ Đăng nhập lại thành công sau khi session hết hạn")
                except Exception as login_error:
                    print(f"Login error: {str(login_error)}")
                    # Tự động giải quyết challenge nếu gặp
                    if "challenge" in str(login_error) or "login_required" in str(login_error):
                        print("=== DETECTED CHALLENGE, ATTEMPTING TO RESOLVE ===")
                        cl.challenge_resolve()
                        print("=== CHALLENGE RESOLVE COMPLETED ===")
        else:
            print("No saved session, logging in with credentials")
            try:
                cl.login(data['username'], data['password'])
            except Exception as e:
                print(f"Login error: {str(e)}")
                # Tự động giải quyết challenge nếu gặp
                if "challenge" in str(e) or "login_required" in str(e):
                    print("=== DETECTED CHALLENGE, ATTEMPTING TO RESOLVE ===")
                    cl.challenge_resolve()
                    print("=== CHALLENGE RESOLVE COMPLETED ===")
        
        # Lưu lại session sau khi đăng nhập thành công
        cl.dump_settings(SESSION_FILE)
        safe_print(f"=== LOGIN SUCCESSFUL ===")
        safe_print(f"✅ Đã lưu session mới vào: {SESSION_FILE}")
        return jsonify({"status": "success", "message": "Dang nhap thanh cong", "user_id": cl.user_id})
    except Exception as e:
        print(f"=== LOGIN FAILED: {str(e)} ===")
        return jsonify({"status": "error", "message": str(e)})

@app.route('/login_by_session', methods=['POST'])
def login_by_session():
    data = request.json
    try:
        print(f"=== ATTEMPTING LOGIN BY SESSION ID ===")
        cl.login_by_sessionid(data['sessionid'])
        print(f"=== LOGIN BY SESSION ID SUCCESSFUL ===")
        return jsonify({"status": "success", "message": "Dang nhap bang session thanh cong"})
    except Exception as e:
        print(f"=== LOGIN BY SESSION ID FAILED: {str(e)} ===")
        return jsonify({"status": "error", "message": str(e)})

@app.route('/send_message', methods=['POST'])
def send_message():
    data = request.json
        # Thử tải lại session để đảm bảo login vẫn còn hiệu lực
    if os.path.exists(SESSION_FILE) and not cl.user_id:
        print(f"Reloading saved session from {SESSION_FILE} before sending message")
        try:
            cl.load_settings(SESSION_FILE)
            # Kiểm tra lại login status
            if not cl.user_id:
                print("Session invalid, need relogin")
                return jsonify({"status": "error", "message": "Phien dang nhap het han, vui long dang nhap lai"})
            else:
                print("Session reloaded successfully")
        except Exception as e:
            print(f"Error reloading session: {str(e)}")

    # Đơn giản hóa: Luôn gửi trực tiếp tới username
    if 'username' in data:
        username = data['username']
        message_content = data['message']
        
        print(f"Sending message to @{username}: {message_content[:30]}...")
        
        # Lấy user_id từ username - Sửa để bỏ qua lỗi extract_user_gql
        user_id = None
        # Thử lấy user_id thông thường
        user_id = cl.user_id_from_username(username)        
        if not user_id:
            return jsonify({"status": "error", "message": f"Khong the tim thay user_id cho @{username}"})
            # Gửi tin nhắn trực tiếp - SỬA: gửi array user_id như code test của user
        result = cl.direct_send(message_content, [user_id])
            
        print(f"✅ Message sent successfully to @{username}")
        
        return jsonify({
            "status": "success", 
            "message_id": result.id,
            "thread_id": result.thread_id,
            "username": username
        })
        
    else:
        return jsonify({"status": "error", "message": "Thieu thong tin username nguoi nhan"})

@app.route('/unsend_message', methods=['POST'])
def unsend_message():
    data = request.json
    try:
        cl.direct_message_delete(data['thread_id'], data['message_id'])
        return jsonify({"status": "success"})
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)})

@app.route('/refresh_session', methods=['POST'])
def refresh_session():
    try:
        print("=== REFRESHING SESSION ===")
        # Kiểm tra xem có session file không
        if not os.path.exists(SESSION_FILE):
            return jsonify({"status": "error", "message": "Khong tim thay session da luu"})
        
        # Thử load session hiện có
        print(f"Reloading session from {SESSION_FILE}")
        cl.load_settings(SESSION_FILE)
        
        # Kiểm tra xem session có hiệu lực không
        if cl.user_id:
            print(f"Session still valid for user ID: {cl.user_id}")
            return jsonify({"status": "success", "message": "Session van con hieu luc", "user_id": cl.user_id})
        else:
            print("Session invalid, returning error")
            return jsonify({"status": "error", "message": "Session da het han, can dang nhap lai"})
    except Exception as e:
        print(f"=== REFRESH SESSION ERROR: {str(e)} ===")
        return jsonify({"status": "error", "message": f"Loi khi refresh session: {str(e)}"})

@app.route('/start_queue_processor', methods=['POST'])
def start_queue_processor():
    global is_running
    if not is_running:
        is_running = True
        thread = threading.Thread(target=queue_processor)
        thread.daemon = True
        thread.start()
        return jsonify({"status": "success", "message": "Bat dau xu ly hang doi"})
    return jsonify({"status": "info", "message": "Dang xu ly hang doi"})

@app.route('/stop_queue_processor', methods=['POST'])
def stop_queue_processor():
    global is_running
    is_running = False
    return jsonify({"status": "success", "message": "Dung xu ly hang doi"})

@app.route('/status', methods=['GET'])
def status():
    return jsonify({
        "status": "success", 
        "is_running": is_running,
        "is_logged_in": cl.user_id is not None
    })

@app.route('/test_connection', methods=['GET'])
def test_connection():
    try:
        # Thử kết nối tới Node.js server
        response = requests.get(f'{NODE_SERVER_URL}/api/health')
        return jsonify({
            "status": "success",
            "connection_test": {
                "url": f'{NODE_SERVER_URL}/api/health',
                "response_status": response.status_code,
                "response_body": response.json() if response.status_code == 200 else None
            }
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"Không thể kết nối tới Node.js server: {str(e)}",
            "url_tested": f'{NODE_SERVER_URL}/api/health'
        })

@app.route('/check_node_connection', methods=['GET'])
def check_node_connection():
    try:
        print(f"=== CHECKING CONNECTION TO NODE.JS SERVER ===")
        print(f"Node.js Server URL: {NODE_SERVER_URL}")
        
        # Thử kết nối tới Node.js server với một endpoint đơn giản
        response = requests.get(f'{NODE_SERVER_URL}/api/health')
        
        if response.status_code == 200:
            print(f"✅ Connection successful! Response: {response.json()}")
            return jsonify({
                "status": "success",
                "message": "Kết nối thành công với Node.js server",
                "node_server_url": NODE_SERVER_URL,
                "response": response.json()
            })
        else:
            print(f"❌ Connection failed with status code: {response.status_code}")
            return jsonify({
                "status": "error",
                "message": f"Kết nối thất bại với mã lỗi: {response.status_code}",
                "node_server_url": NODE_SERVER_URL
            })
    except Exception as e:
        print(f"❌ Connection error: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"Lỗi kết nối: {str(e)}",
            "node_server_url": NODE_SERVER_URL
        })

def queue_processor():
    global is_running, cl
    print("Queue processor started")
    
    # Kiểm tra và tải session khi khởi động queue processor
    session_valid = False
    if os.path.exists(SESSION_FILE):
        print("🔄 Tải session từ file khi khởi động queue_processor")
        cl.load_settings(SESSION_FILE)
        
        # Kiểm tra tính hợp lệ của session
        try:
            print("🔍 Đang xác thực session...")
            cl.account_info()  # API kiểm tra tài khoản - sẽ ném lỗi nếu session hết hạn
            session_valid = True
            print(f"✅ Session hợp lệ cho user_id: {cl.user_id}")
        except Exception as e:
            print(f"❌ Session không hợp lệ hoặc hết hạn: {str(e)}")
            print("🗑️ Xóa file session cũ vì đã hết hạn")
            try:
                os.remove(SESSION_FILE)
                print(f"✅ Đã xóa file session cũ: {SESSION_FILE}")
            except Exception as del_error:
                print(f"⚠️ Không thể xóa file session cũ: {str(del_error)}")
            
            print("⚠️ Queue sẽ gặp lỗi khi gửi tin nhắn! Vui lòng đăng nhập lại qua API /login")
    else:
        print("⚠️ Không tìm thấy file session! Vui lòng đăng nhập qua API /login trước")
    
    print(f"Connecting to Node.js server at: {NODE_SERVER_URL}")
    
    while is_running:
        try:
            # Lấy tin nhắn tiếp theo từ hàng chờ
            print(f"\n--- Checking for messages in queue ---")
            response = requests.get(f'{NODE_SERVER_URL}/api/message-queue/next')
            data = response.json()
            
            # Kiểm tra nếu có tin nhắn trong hàng đợi
            if data['success'] and data['message']:
                message = data['message']
                print(f"✉️ Tin nhắn mới: ID={message['id']} cho @{message['username']}")
                print(f"✉️ Nội dung: {message['message']}")
                
                # Xử lý thay thế biến trong nội dung tin nhắn
                msg_content = message['message']
                # Thay thế các biến trong nội dung tin nhắn
                msg_content = msg_content.replace("{{content}}", message['original_comment'])
                msg_content = msg_content.replace("{content}", message['original_comment'])
                msg_content = msg_content.replace("{username}", message['username'])
                msg_content = msg_content.replace("{comment}", message['original_comment'])
                
                # Cập nhật nội dung tin nhắn sau khi thay thế biến
                message['message'] = msg_content
                print(f"✉️ Nội dung sau khi thay thế biến: {message['message']}")
                
                # Kiểm tra session trước khi gửi
                if not session_valid:
                    print("⚠️ Session không hợp lệ hoặc hết hạn, thử tải lại session")
                    if os.path.exists(SESSION_FILE):
                        cl.load_settings(SESSION_FILE)
                        # Kiểm tra lại session sau khi tải
                        try:
                            print("🔍 Đang xác thực session sau khi tải lại...")
                            cl.account_info()
                            session_valid = True
                            print(f"✅ Session hợp lệ cho user_id: {cl.user_id}")
                        except Exception as e:
                            print(f"❌ Session không hợp lệ hoặc hết hạn sau khi tải lại: {str(e)}")
                            print("🗑️ Xóa file session cũ vì đã hết hạn")
                            try:
                                os.remove(SESSION_FILE)
                                print(f"✅ Đã xóa file session cũ: {SESSION_FILE}")
                                # Reset client
                                cl = Client()
                            except Exception as del_error:
                                print(f"⚠️ Không thể xóa file session cũ: {str(del_error)}")
                            print("⚠️ Vui lòng đăng nhập lại qua API /login trước")
                    else:
                        print("⚠️ Không tìm thấy file session sau khi tải lại! Vui lòng đăng nhập qua API /login trước")
                
                try:
                    # Chỉ tiếp tục nếu session hợp lệ
                    if not session_valid:
                        print("❌ Không thể gửi tin nhắn: Session không hợp lệ!")
                        # Đánh dấu tin nhắn để thử lại sau
                        requests.post(f'{NODE_SERVER_URL}/api/message-queue/update-status', json={
                            'id': message['id'],
                            'status': 'pending',
                            'error': 'Session không hợp lệ, đợi đăng nhập lại'
                        })
                        # Random delay 5-10s thay vì chờ cố định 10s
                        delay = random.randint(5, 10)
                        print(f"⏳ Chờ {delay}s trước khi kiểm tra lại...")
                        time.sleep(delay)
                        continue

                    # Thêm delay để đảm bảo trạng thái processing được hiển thị
                    print(f"⏳ Đang xử lý tin nhắn cho @{message['username']}...")
                    time.sleep(2)  # Delay 2 giây để hiển thị trạng thái processing

                    # Lấy user_id từ username
                    user_id = cl.user_id_from_username(message['username'])

                    # Gửi tin nhắn qua Instagrapi
                    result = cl.direct_send(message['message'], [user_id])

                    # Log toàn bộ kết quả trả về từ Instagrapi
                    print('=== INSTAGRAPI SEND RESULT ===')
                    print(result)
                    print('type(result):', type(result))
                    if hasattr(result, '__dict__'):
                        print('result.__dict__:', result.__dict__)
                    else:
                        print('result as dict:', dict(result) if isinstance(result, dict) else str(result))
                    print('==============================')

                    # Cập nhật trạng thái thành 'completed' thay vì xóa khỏi hàng chờ
                    update_response = requests.post(f'{NODE_SERVER_URL}/api/message-queue/update-status', json={
                        'id': message['id'],
                        'status': 'completed',
                        'completed_at': datetime.now().isoformat(),
                        'messageContent': message['message'],
                        'messageId': getattr(result, 'id', None),
                        'threadId': str(getattr(result, 'thread_id', ''))
                    })
                    
                    if update_response.status_code == 200:
                        print(f"✅ Cập nhật trạng thái tin nhắn thành công: {message['id']} -> completed")
                    else:
                        print(f"❌ Không thể cập nhật trạng thái tin nhắn: {message['id']}")
                except Exception as e:
                    print(f"❌ Lỗi khi gửi tin nhắn: {str(e)}")
                    session_valid = False  # Đánh dấu session không còn hợp lệ
                    
                    # Xử lý lỗi login_required hoặc challenge
                    if "login_required" in str(e) or "challenge" in str(e):
                        print("⚠️ Phát hiện lỗi đăng nhập hoặc challenge, đang thử giải quyết...")
                        
                        # Xóa file session cũ vì đã hết hạn
                        if os.path.exists(SESSION_FILE):
                            try:
                                os.remove(SESSION_FILE)
                                print(f"✅ Đã xóa file session cũ: {SESSION_FILE}")
                                # Reset client
                                cl = Client()
                            except Exception as del_error:
                                print(f"⚠️ Không thể xóa file session cũ: {str(del_error)}")
                        
                        try:
                            # Thử giải quyết challenge
                            cl.challenge_resolve()
                            print("✅ Đã giải quyết challenge thành công")
                            
                            # Thử xác thực session sau khi giải quyết challenge
                            try:
                                cl.account_info()
                                session_valid = True
                                print("✅ Session hợp lệ sau khi giải quyết challenge")
                                
                                # Lưu lại session sau khi giải quyết
                                cl.dump_settings(SESSION_FILE)
                                print(f"✅ Đã lưu session mới vào: {SESSION_FILE}")
                            except:
                                print("❌ Session vẫn không hợp lệ sau khi giải quyết challenge")
                            
                            # Đánh dấu tin nhắn để gửi lại sau
                            print("⏳ Đánh dấu tin nhắn để thử lại sau")
                            requests.post(f'{NODE_SERVER_URL}/api/message-queue/update-status', json={
                                'id': message['id'],
                                'status': 'pending',
                                'error': 'Đang thử lại sau khi xử lý challenge'
                            })
                        except Exception as challenge_error:
                            print(f"❌ Không thể giải quyết challenge: {str(challenge_error)}")
                            # Đánh dấu tin nhắn thất bại
                            requests.post(f'{NODE_SERVER_URL}/api/message-queue/update-status', json={
                                'id': message['id'],
                                'status': 'failed',
                                'error': f'Challenge failed: {str(challenge_error)}'
                            })
                    else:
                        # Lỗi khác, đánh dấu tin nhắn thất bại
                        requests.post(f'{NODE_SERVER_URL}/api/message-queue/update-status', json={
                            'id': message['id'],
                            'status': 'failed',
                            'error': str(e)
                        })
                
                # Random delay 5-10s để tránh phát hiện spam
                delay = random.randint(5, 10)
                print(f"⏳ Chờ {delay}s trước khi gửi tin nhắn tiếp theo...")
                time.sleep(delay)
            else:
                # Không có tin nhắn trong hàng đợi
                print("📭 Không có tin nhắn trong hàng đợi")
                # Random delay 5-10s thay vì chờ cố định 10s
                delay = random.randint(5, 10)
                print(f"⏳ Chờ {delay}s trước khi kiểm tra lại...")
                time.sleep(delay)
                
        except Exception as e:
            # Xử lý lỗi đơn giản
            print(f"❌ Lỗi: {str(e)}")
            session_valid = False  # Đánh dấu session không còn hợp lệ
            
            # Kiểm tra nếu là lỗi login
            if "login_required" in str(e) or "challenge" in str(e):
                print("⚠️ Phát hiện lỗi đăng nhập, đang thử giải quyết challenge...")
                
                # Xóa file session cũ vì đã hết hạn
                if os.path.exists(SESSION_FILE):
                    try:
                        os.remove(SESSION_FILE)
                        print(f"✅ Đã xóa file session cũ: {SESSION_FILE}")
                        # Reset client
                        cl = Client()
                    except Exception as del_error:
                        print(f"⚠️ Không thể xóa file session cũ: {str(del_error)}")
                
                try:
                    cl.challenge_resolve()
                    print("✅ Đã giải quyết challenge thành công")
                    # Thử xác thực session sau khi giải quyết challenge
                    try:
                        cl.account_info()
                        session_valid = True
                        print("✅ Session hợp lệ sau khi giải quyết challenge")
                        cl.dump_settings(SESSION_FILE)
                        print(f"✅ Đã lưu session mới vào: {SESSION_FILE}")
                    except:
                        print("❌ Session vẫn không hợp lệ sau khi giải quyết challenge")
                except Exception as challenge_error:
                    print(f"❌ Không thể giải quyết challenge: {str(challenge_error)}")
            
            # Random delay 5-10s thay vì chờ cố định 10s
            delay = random.randint(5, 10)
            print(f"⏳ Chờ {delay}s trước khi thử lại...")
            time.sleep(delay)

def try_auto_login():
    """Thử đăng nhập tự động với credentials đã lưu"""
    try:
        # Gọi API để lấy credentials đã lưu
        response = requests.get('http://localhost:3000/api/instagrapi/credentials')
        if response.status_code == 200:
            data = response.json()
            if data.get('hasCredentials'):
                safe_print("🔑 Tìm thấy thông tin đăng nhập đã lưu, thử đăng nhập tự động...")

                # Gọi auto-login endpoint
                auto_login_response = requests.post('http://localhost:3000/api/messenger/instagrapi/auto-login')
                if auto_login_response.status_code == 200:
                    auto_login_data = auto_login_response.json()
                    if auto_login_data.get('success'):
                        safe_print("✅ Đăng nhập tự động thành công!")
                        return True
                    else:
                        safe_print(f"❌ Đăng nhập tự động thất bại: {auto_login_data.get('message', 'Unknown error')}")
                else:
                    safe_print("❌ Lỗi khi gọi auto-login API")
            else:
                safe_print("ℹ️ Không có thông tin đăng nhập đã lưu")
        else:
            safe_print("❌ Không thể kiểm tra credentials đã lưu")
    except Exception as e:
        safe_print(f"❌ Lỗi khi thử đăng nhập tự động: {str(e)}")

    return False

if __name__ == '__main__':
    safe_print("🚀 Khởi động InstagrapiService...")

    # Thử đăng nhập tự động sau 3 giây (để đảm bảo main server đã khởi động)
    def delayed_auto_login():
        time.sleep(3)
        try_auto_login()

    # Chạy auto-login trong thread riêng để không block main app
    auto_login_thread = threading.Thread(target=delayed_auto_login, daemon=True)
    auto_login_thread.start()

    app.run(host='0.0.0.0', port=5000, debug=True)
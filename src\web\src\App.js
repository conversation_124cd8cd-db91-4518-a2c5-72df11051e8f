import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import io from 'socket.io-client';

// Components
import Layout from './components/Layout';
import Comments from './pages/Comments';
import AutoMessages from './pages/AutoMessages';
import History from './pages/History';
import Settings from './pages/Settings';
import CustomerManagement from './pages/CustomerManagement';
import LoadingScreen from './components/LoadingScreen';
import ConnectionStatus from './components/ConnectionStatus';

// Context
import { SocketProvider } from './contexts/SocketContext';
import { AppProvider } from './contexts/AppContext';
import { CancelSettingsProvider } from './contexts/CancelSettingsContext';

// Styles
import './App-clean.css';

function App() {
  const [socket, setSocket] = useState(null);
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Dynamic server URL detection for mobile compatibility
  const getServerUrl = () => {
    const hostname = window.location.hostname;
    const port = '3001';

    // If accessing via IP address (mobile), use that IP
    // If accessing via localhost (desktop), use localhost
    if (hostname === 'localhost' || hostname === '127.0.0.1') {
      return `http://localhost:${port}`;
    } else {
      return `http://${hostname}:${port}`;
    }
  };

  const [serverUrl, setServerUrl] = useState(getServerUrl());

  // Determine base path based on environment and current URL
  const getBasePath = () => {
    // In development (port 3002), use root path
    if (process.env.NODE_ENV === 'development' || window.location.port === '3002') {
      return '';
    }
    // In production (port 3001), use /web path
    return '/web';
  };

  const basePath = getBasePath();

  useEffect(() => {
    // Initialize socket connection
    initializeSocket();

    // Set loading to false after initial setup - reduce timeout for faster loading
    const loadingTimeout = setTimeout(() => {
      console.log('Loading timeout reached, showing app...');
      setIsLoading(false);
    }, 500); // Reduced from 1000ms to 500ms

    return () => {
      clearTimeout(loadingTimeout);
      if (socket) {
        socket.disconnect();
      }
    };
  }, []);

  const initializeSocket = () => {
    try {
      console.log('=== INITIALIZING SOCKET ===');
      console.log('Server URL:', serverUrl);

      const newSocket = io(serverUrl, {
        transports: ['websocket', 'polling'],
        timeout: 5000, // Reduced timeout
        reconnection: true,
        reconnectionAttempts: 5, // Reduced attempts
        reconnectionDelay: 1000,
        forceNew: true,
        autoConnect: true,
        cors: {
          origin: "*",
          methods: ["GET", "POST"]
        }
      });

      // Debug socket connection state
      console.log('Socket created:', newSocket);
      console.log('Socket connected:', newSocket.connected);
      console.log('Socket disconnected:', newSocket.disconnected);

      newSocket.on('connect', () => {
        console.log('=== SOCKET CONNECTED ===');
        console.log('Socket ID:', newSocket.id);
        console.log('Socket transport:', newSocket.io.engine.transport.name);
        setIsConnected(true);

        // Test emit to verify connection
        console.log('Testing socket connection...');
        newSocket.emit('test-connection', { message: 'Hello from client' });
      });

      newSocket.on('test-response', (data) => {
        console.log('=== TEST RESPONSE RECEIVED ===', data);
      });

      newSocket.on('disconnect', (reason) => {
        console.log('Disconnected from server:', reason);
        setIsConnected(false);
      });

      newSocket.on('connect_error', (error) => {
        console.error('Connection error:', error);
        setIsConnected(false);
        // Don't block app loading due to connection errors
        if (isLoading) {
          console.log('Socket connection failed, but allowing app to load...');
          setTimeout(() => setIsLoading(false), 100);
        }
      });

      newSocket.on('reconnect', (attemptNumber) => {
        console.log('Reconnected after', attemptNumber, 'attempts');
        setIsConnected(true);
      });

      newSocket.on('reconnect_error', (error) => {
        console.error('Reconnection error:', error);
      });

      newSocket.on('reconnect_failed', () => {
        console.error('Failed to reconnect to server');
        setIsConnected(false);
      });

      setSocket(newSocket);

      // Fallback timeout - if socket doesn't connect within 3 seconds, allow app to load anyway
      setTimeout(() => {
        if (!newSocket.connected && isLoading) {
          console.log('Socket connection timeout, allowing app to load without connection...');
          setIsLoading(false);
        }
      }, 3000);

    } catch (error) {
      console.error('Failed to initialize socket:', error);
      setIsConnected(false);
      // Allow app to load even if socket initialization fails
      if (isLoading) {
        setTimeout(() => setIsLoading(false), 100);
      }
    }
  };

  const reconnectSocket = () => {
    if (socket) {
      socket.disconnect();
    }
    initializeSocket();
  };

  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    <AppProvider>
      <CancelSettingsProvider>
        <SocketProvider socket={socket} isConnected={isConnected}>
        <Router basename={basePath}>
          <div className="App min-h-screen bg-gray-50">
            {/* Connection Status */}
            <ConnectionStatus
              isConnected={isConnected}
              onReconnect={reconnectSocket}
            />

            {/* Main App Layout */}
            <Layout>
              <Routes>
                <Route path="/" element={<Navigate to="/comments" replace />} />
                <Route path="/comments" element={<Comments />} />
                <Route path="/auto-messages" element={<AutoMessages />} />
                <Route path="/history" element={<History />} />
                <Route path="/customers" element={<CustomerManagement />} />
                <Route path="/settings" element={<Settings />} />
                <Route path="*" element={<Navigate to="/comments" replace />} />
              </Routes>
            </Layout>

            {/* Toast Notifications */}
            <Toaster
              position="top-center"
              toastOptions={{
                duration: 4000,
                style: {
                  background: '#363636',
                  color: '#fff',
                  fontSize: '14px',
                  borderRadius: '8px',
                  padding: '12px 16px',
                },
                success: {
                  iconTheme: {
                    primary: '#10b981',
                    secondary: '#fff',
                  },
                },
                error: {
                  iconTheme: {
                    primary: '#ef4444',
                    secondary: '#fff',
                  },
                },
              }}
              containerStyle={{
                top: 20,
                left: 20,
                bottom: 20,
                right: 20,
              }}
              gutter={8}
              containerClassName="toast-container"
            />
          </div>
        </Router>
      </SocketProvider>
      </CancelSettingsProvider>
    </AppProvider>
  );
}

export default App;

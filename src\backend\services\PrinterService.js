const EventEmitter = require('events');
const fs = require('fs').promises;
const path = require('path');
const { createCanvas } = require('canvas');
const sharp = require('sharp');

class PrinterService extends EventEmitter {
  constructor(logger, database = null) {
    super();
    this.logger = logger;
    this.database = database;
    this.printSettings = null;
    this.printerSettings = {
      name: 'HPRT TP80N',
      connectionType: 'usb', // 'usb', 'network'
      networkSettings: {
        ip: '*************',
        port: 9100,
        timeout: 5000
      },
      usbSettings: {
        vendorId: '0x0fe6', // HPRT vendor ID
        productId: '0x811e', // TP80N product ID
        interface: 0
      },
      paperWidth: 80, // mm
      characterWidth: 48, // characters per line for 80mm
      lineHeight: 24, // dots
      encoding: 'utf8',
      cutType: 'partial', // full, partial, none
      cashdrawer: false,
      printMode: 'bitmap', // 'text', 'bitmap'
      bitmapSettings: {
        width: 576, // pixels for 80mm (72 DPI * 80mm / 25.4)
        fontSize: 16,
        fontFamily: 'Arial, sans-serif',
        lineSpacing: 4,
        padding: 10,
        dpi: 203 // HPRT TP80N DPI
      },
      margins: {
        left: 2,
        right: 2,
        top: 5,
        bottom: 10
      }
    };

    this.printFormats = {
      comment: {
        header: true,
        timestamp: true,
        username: true,
        content: true,
        footer: true,
        qrCode: false,
        barcode: false
      },
      receipt: {
        header: true,
        timestamp: true,
        username: true,
        content: true,
        footer: true,
        qrCode: true,
        barcode: false,
        price: true,
        total: true
      }
    };

    this.templates = {
      header: '================================\n    INSTAGRAM LIVE ORDER\n================================',
      footer: '================================\nCảm ơn quý khách!\nInstagram: @your_shop_name\n================================',
      separator: '--------------------------------'
    };

    this.isInitialized = false;
  }

  async initialize() {
    try {
      // Load settings from database first (primary source)
      if (this.database) {
        try {
          const dbSettings = await this.database.getPrintSettings();
          if (dbSettings && dbSettings.printer) {
            this.printerSettings = { ...this.printerSettings, ...dbSettings.printer };
            this.logger.info('Loaded printer settings from database');
          }
        } catch (error) {
          this.logger.warn('Failed to load settings from database, using defaults:', error);
        }
      }

      // Fallback to file-based settings if database fails
      await this.loadSettings();
      await this.detectPrinters();
      this.isInitialized = true;
      this.logger.info('PrinterService initialized successfully');
      return true;
    } catch (error) {
      this.logger.error('Failed to initialize PrinterService:', error);
      return false;
    }
  }

  async loadSettings() {
    try {
      const settingsPath = path.join(__dirname, '../data/printer-settings.json');
      const data = await fs.readFile(settingsPath, 'utf8');
      const settings = JSON.parse(data);

      this.printerSettings = { ...this.printerSettings, ...settings.printer };
      this.printFormats = { ...this.printFormats, ...settings.formats };
      this.templates = { ...this.templates, ...settings.templates };

      this.logger.info('Printer settings loaded successfully');
    } catch (error) {
      this.logger.warn('No existing printer settings found, using defaults');
      await this.saveSettings();
    }
  }

  async saveSettings() {
    try {
      const settingsPath = path.join(__dirname, '../data/printer-settings.json');
      const settings = {
        printer: this.printerSettings,
        formats: this.printFormats,
        templates: this.templates,
        lastUpdated: new Date().toISOString()
      };

      // Ensure directory exists
      await fs.mkdir(path.dirname(settingsPath), { recursive: true });
      await fs.writeFile(settingsPath, JSON.stringify(settings, null, 2));

      this.logger.info('Printer settings saved successfully');
      return true;
    } catch (error) {
      this.logger.error('Failed to save printer settings:', error);
      return false;
    }
  }

  async detectPrinters() {
    try {
      // For Windows, we'll use system printer detection
      if (process.platform === 'win32') {
        const { exec } = require('child_process');
        const { promisify } = require('util');
        const execAsync = promisify(exec);

        const { stdout } = await execAsync('wmic printer get name,status /format:csv');
        const printers = this.parsePrinterList(stdout);

        this.logger.info(`Detected ${printers.length} printers:`, printers.map(p => p.name));
        return printers;
      }

      return [];
    } catch (error) {
      this.logger.error('Failed to detect printers:', error);
      return [];
    }
  }

  parsePrinterList(csvOutput) {
    const lines = csvOutput.split('\n').filter(line => line.trim());
    const printers = [];

    for (let i = 1; i < lines.length; i++) {
      const parts = lines[i].split(',');
      if (parts.length >= 3 && parts[1] && parts[2]) {
        printers.push({
          name: parts[1].trim(),
          status: parts[2].trim(),
          isDefault: false,
          isThermal: parts[1].toLowerCase().includes('hprt') ||
            parts[1].toLowerCase().includes('thermal') ||
            parts[1].toLowerCase().includes('pos')
        });
      }
    }

    return printers;
  }

  formatCommentForPrint(comment, format = 'comment') {
    const formatConfig = this.printFormats[format];
    const width = this.printerSettings.characterWidth;
    let output = '';

    // Header
    if (formatConfig.header) {
      output += this.centerText(this.templates.header, width) + '\n\n';
    }

    // Timestamp
    if (formatConfig.timestamp) {
      const timestamp = new Date(comment.timestamp).toLocaleString('vi-VN', {
        timeZone: 'Asia/Ho_Chi_Minh',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
      output += this.centerText(`Thời gian: ${timestamp}`, width) + '\n';
      output += this.templates.separator + '\n';
    }

    // Username
    if (formatConfig.username) {
      const usernameText = comment.isBackup
        ? `@${comment.username} (dự bị)`
        : `@${comment.username}`;
      output += `Khách hàng: ${usernameText}\n`;
    }

    // Content
    if (formatConfig.content) {
      output += `Nội dung:\n`;
      output += this.wrapText(comment.text, width - 2) + '\n';
    }

    // Price (for receipt format)
    if (formatConfig.price && comment.price) {
      output += this.templates.separator + '\n';
      output += this.rightAlign(`Giá: ${comment.price}đ`, width) + '\n';
    }

    // Total (for receipt format)
    if (formatConfig.total && comment.total) {
      output += this.rightAlign(`Tổng: ${comment.total}đ`, width) + '\n';
    }

    // Footer
    if (formatConfig.footer) {
      output += '\n' + this.templates.separator + '\n';
      output += this.centerText(this.templates.footer, width) + '\n';
    }

    // Cut command for thermal printer
    output += '\n\n\n';
    if (this.printerSettings.cutType !== 'none') {
      output += '\x1D\x56\x42\x00'; // ESC/POS cut command
    }

    return output;
  }

  centerText(text, width) {
    const lines = text.split('\n');
    return lines.map(line => {
      const padding = Math.max(0, Math.floor((width - line.length) / 2));
      return ' '.repeat(padding) + line;
    }).join('\n');
  }

  rightAlign(text, width) {
    const padding = Math.max(0, width - text.length);
    return ' '.repeat(padding) + text;
  }

  wrapText(text, width) {
    const words = text.split(' ');
    const lines = [];
    let currentLine = '';

    for (const word of words) {
      if ((currentLine + word).length <= width) {
        currentLine += (currentLine ? ' ' : '') + word;
      } else {
        if (currentLine) lines.push(currentLine);
        currentLine = word;
      }
    }

    if (currentLine) lines.push(currentLine);
    return lines.join('\n');
  }

  async printComment(comment, format = 'comment') {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      // Debug: Log current printer settings
      this.logger.info(`PrintComment - Connection Type: ${this.printerSettings.connectionType}`);
      this.logger.info(`PrintComment - Print Mode: ${this.printerSettings.printMode}`);
      this.logger.info(`PrintComment - Full Settings:`, this.printerSettings);

      let printData;
      let result;

      if (this.printerSettings.printMode === 'bitmap') {
        // Generate bitmap for Vietnamese text support
        printData = await this.generateBitmapPrint(comment, format);
      } else {
        // Traditional text mode
        const formattedContent = this.formatCommentForPrint(comment, format);
        printData = this.convertToESCPOS(formattedContent);
      }

      // Choose printing method based on connection type
      if (this.printerSettings.connectionType === 'network') {
        result = await this.printToNetworkPrinter(printData);
      } else if (this.printerSettings.connectionType === 'usb') {
        result = await this.printToUSBPrinter(printData);
      } else if (this.printerSettings.connectionType === 'file') {
        // Print to text file for testing
        const formattedContent = this.formatCommentForPrint(comment, format);
        result = await this.printToFile(formattedContent, comment);
      } else {
        // For system printer, save bitmap as image and print
        if (this.printerSettings.printMode === 'bitmap') {
          result = await this.printBitmapToSystemPrinter(printData, comment);
        } else {
          const formattedContent = this.formatCommentForPrint(comment, format);
          result = await this.printToWindowsPrinter(formattedContent);
        }
      }

      // Mark comment as printed in database if print was successful
      if (result.success && this.database && (comment.pk || comment.id)) {
        try {
          // Determine print type based on comment data
          const printType = comment.isBackup ? 'backup' : 'comment';

          // Use pk if available (from API), fallback to id for backward compatibility
          const commentIdentifier = comment.pk || comment.id;

          // Updated method signature: pass comment data directly instead of looking up by ID
          await this.database.markCommentAsPrinted(
            commentIdentifier,
            comment.username,
            comment.text,
            printType
          );
          this.logger.info(`Comment ${commentIdentifier} marked as printed in database`);
        } catch (dbError) {
          this.logger.error('Failed to mark comment as printed in database:', dbError);
          // Don't throw error - print was successful, just database update failed
        }
      }

      return result;

    } catch (error) {
      this.logger.error('Failed to print comment:', error);
      throw error;
    }
  }

  async printToWindowsPrinter(content) {
    try {
      const { exec } = require('child_process');
      const { promisify } = require('util');
      const execAsync = promisify(exec);
      const tempFile = path.join(__dirname, '../temp/print_temp.txt');

      // Ensure temp directory exists
      await fs.mkdir(path.dirname(tempFile), { recursive: true });

      // Write content to temp file
      await fs.writeFile(tempFile, content, 'utf8');

      // Print using Windows print command
      const printerName = this.printerSettings.selectedPrinter || 'default';
      const command = `print /D:"${printerName}" "${tempFile}"`;

      await execAsync(command);

      // Clean up temp file
      setTimeout(async () => {
        try {
          await fs.unlink(tempFile);
        } catch (error) {
          // Ignore cleanup errors
        }
      }, 5000);

      this.logger.info('Print job sent successfully');
      return { success: true, method: 'windows_printer' };

    } catch (error) {
      this.logger.error('Windows printing failed:', error);
      throw error;
    }
  }

  async printToFile(content, comment) {
    try {
      // Get file settings from print settings
      const fileSettings = this.printSettings?.printer?.fileSettings || {
        outputPath: '',
        fileNameFormat: 'timestamp'
      };

      // Determine output directory
      const outputDir = fileSettings.outputPath || path.join(__dirname, '../../prints');
      await fs.mkdir(outputDir, { recursive: true });

      // Generate filename based on format
      let filename;
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);

      switch (fileSettings.fileNameFormat) {
        case 'username':
          filename = `print_${comment.username}_${timestamp}.txt`;
          break;
        case 'sequential':
          // Find next sequential number
          const files = await fs.readdir(outputDir);
          const printFiles = files.filter(f => f.startsWith('print_') && f.endsWith('.txt'));
          const numbers = printFiles.map(f => {
            const match = f.match(/print_(\d+)\.txt/);
            return match ? parseInt(match[1]) : 0;
          });
          const nextNum = Math.max(0, ...numbers) + 1;
          filename = `print_${nextNum.toString().padStart(3, '0')}.txt`;
          break;
        default: // timestamp
          filename = `print_${timestamp}.txt`;
      }

      const filepath = path.join(outputDir, filename);

      // Add header with metadata
      const usernameText = comment.isBackup
        ? `@${comment.username} (dự bị)`
        : `@${comment.username}`;

      const header = `=== INSTAGRAM LIVE COMMENT PRINT ===
Printed at: ${new Date().toLocaleString('vi-VN', {
        timeZone: 'Asia/Ho_Chi_Minh',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })}
Comment ID: ${comment.id}
Username: ${usernameText}
Original timestamp: ${comment.timestamp}
=====================================

`;

      const fullContent = header + content + '\n\n--- End of Print ---\n';

      // Write to file
      await fs.writeFile(filepath, fullContent, 'utf8');

      this.logger.info(`Content printed to file: ${filepath}`);
      return {
        success: true,
        method: 'file',
        filepath: filepath,
        filename: filename
      };

    } catch (error) {
      this.logger.error('Failed to print to file:', error);
      throw error;
    }
  }

  // Settings management methods
  updatePrinterSettings(newSettings) {
    this.printerSettings = { ...this.printerSettings, ...newSettings };
    return this.saveSettings();
  }

  updatePrintFormats(newFormats) {
    this.printFormats = { ...this.printFormats, ...newFormats };
    return this.saveSettings();
  }

  updateTemplates(newTemplates) {
    this.templates = { ...this.templates, ...newTemplates };
    return this.saveSettings();
  }

  getSettings() {
    return {
      printer: this.printerSettings,
      formats: this.printFormats,
      templates: this.templates
    };
  }

  async printToNetworkPrinter(printData) {
    try {
      const net = require('net');
      const { ipAddress, port, timeout = 5000 } = this.printerSettings.networkSettings;

      if (!ipAddress || !port) {
        throw new Error('IP address and port are required for network printing');
      }

      return new Promise((resolve, reject) => {
        const client = new net.Socket();

        // Set timeout
        client.setTimeout(timeout);

        client.connect(port, ipAddress, () => {
          this.logger.info(`Connected to network printer at ${ipAddress}:${port}`);

          // Send print data (either ESC/POS text or bitmap)
          client.write(printData);

          // Close connection after sending
          setTimeout(() => {
            client.destroy();
            resolve({ success: true, method: 'network_printer', ip: ipAddress, port });
          }, 1000);
        });

        client.on('timeout', () => {
          client.destroy();
          reject(new Error(`Network printer timeout: ${ipAddress}:${port}`));
        });

        client.on('error', (error) => {
          reject(new Error(`Network printer error: ${error.message}`));
        });
      });

    } catch (error) {
      this.logger.error('Network printing failed:', error);
      throw error;
    }
  }

  async printToUSBPrinter(printData) {
    try {
      // For USB printing, we'll use node-usb or fallback to system printer
      const usb = require('usb');
      const { vendorId, productId } = this.printerSettings.usbSettings;

      // Convert hex strings to numbers
      const vid = parseInt(vendorId, 16);
      const pid = parseInt(productId, 16);

      const device = usb.findByIds(vid, pid);

      if (!device) {
        this.logger.warn('USB printer not found, falling back to system printer');
        if (this.printerSettings.printMode === 'bitmap') {
          return await this.printBitmapToSystemPrinter(printData);
        } else {
          return await this.printToWindowsPrinter(printData);
        }
      }

      device.open();
      const usbInterface = device.interface(0);

      if (usbInterface.isKernelDriverActive()) {
        usbInterface.detachKernelDriver();
      }

      usbInterface.claim();

      // Find OUT endpoint
      const outEndpoint = usbInterface.endpoints.find(ep => ep.direction === 'out');

      if (!outEndpoint) {
        throw new Error('No OUT endpoint found on USB printer');
      }

      return new Promise((resolve, reject) => {
        outEndpoint.transfer(printData, (error) => {
          usbInterface.release();
          device.close();

          if (error) {
            reject(new Error(`USB transfer error: ${error.message}`));
          } else {
            this.logger.info('USB print completed successfully');
            resolve({ success: true, method: 'usb_printer', vendorId, productId });
          }
        });
      });

    } catch (error) {
      this.logger.error('USB printing failed:', error);
      // Fallback to system printer
      this.logger.info('Falling back to system printer');
      if (this.printerSettings.printMode === 'bitmap') {
        return await this.printBitmapToSystemPrinter(printData);
      } else {
        return await this.printToWindowsPrinter(printData);
      }
    }
  }

  convertToESCPOS(content) {
    // ESC/POS command constants
    const ESC = '\x1B';
    const GS = '\x1D';

    let escPosData = Buffer.from([]);

    // Initialize printer
    escPosData = Buffer.concat([escPosData, Buffer.from(ESC + '@')]);

    // Set character set to UTF-8
    escPosData = Buffer.concat([escPosData, Buffer.from(ESC + 't\x03')]);

    // Convert content to buffer
    const contentBuffer = Buffer.from(content, 'utf8');
    escPosData = Buffer.concat([escPosData, contentBuffer]);

    // Add cut command based on settings
    if (this.printerSettings.cutType === 'full') {
      escPosData = Buffer.concat([escPosData, Buffer.from(GS + 'V\x00')]);
    } else if (this.printerSettings.cutType === 'partial') {
      escPosData = Buffer.concat([escPosData, Buffer.from(GS + 'V\x01')]);
    }

    // Open cash drawer if enabled
    if (this.printerSettings.cashdrawer) {
      escPosData = Buffer.concat([escPosData, Buffer.from(ESC + 'p\x00\x19\xFA')]);
    }

    return escPosData;
  }

  async testNetworkConnection() {
    try {
      const net = require('net');
      const { ipAddress, port, timeout = 5000 } = this.printerSettings.networkSettings;

      if (!ipAddress || !port) {
        throw new Error('IP address and port are required');
      }

      return new Promise((resolve, reject) => {
        const client = new net.Socket();
        client.setTimeout(timeout);

        client.connect(port, ipAddress, () => {
          client.destroy();
          resolve({ success: true, message: `Connected to ${ipAddress}:${port}` });
        });

        client.on('timeout', () => {
          client.destroy();
          reject(new Error(`Connection timeout: ${ipAddress}:${port}`));
        });

        client.on('error', (error) => {
          reject(new Error(`Connection failed: ${error.message}`));
        });
      });
    } catch (error) {
      throw new Error(`Network test failed: ${error.message}`);
    }
  }

  async testUSBConnection() {
    try {
      const usb = require('usb');
      const { vendorId, productId } = this.printerSettings.usbSettings;

      const vid = parseInt(vendorId, 16);
      const pid = parseInt(productId, 16);

      const device = usb.findByIds(vid, pid);

      if (!device) {
        throw new Error(`USB device not found: ${vendorId}:${productId}`);
      }

      return { success: true, message: `USB printer found: ${vendorId}:${productId}` };
    } catch (error) {
      throw new Error(`USB test failed: ${error.message}`);
    }
  }

  async updatePrintSettings(settings) {
    this.printSettings = settings;

    // Also update printerSettings for backward compatibility
    if (settings.printer) {
      this.printerSettings = { ...this.printerSettings, ...settings.printer };
    }

    // Save to both database and file for persistence
    if (this.database) {
      try {
        await this.database.savePrintSettings(settings);
        this.logger.info('Print settings saved to database');
      } catch (error) {
        this.logger.error('Failed to save print settings to database:', error);
      }
    }

    // Also save to file as backup
    await this.saveSettings();

    this.logger.info('Print settings updated and persisted');
    this.logger.info('Updated printer settings:', this.printerSettings);
  }

  async generateBitmapPrint(comment, format = 'comment') {
    try {
      // Load print settings if not loaded
      if (!this.printSettings && this.database) {
        this.printSettings = await this.database.getPrintSettings();
      }

      // Use print settings or fallback to defaults
      const allSettings = this.printSettings || this.getDefaultPrintSettings();
      const formatSettings = format === 'history' ? allSettings.userHistory : allSettings.singleComment;
      const paperWidthMm = 80; // Fixed 80mm paper width
      const padding = formatSettings.padding || 8;

      // Convert mm to pixels (203 DPI for HPRT TP80N)
      const width = Math.round((paperWidthMm / 25.4) * 203);

      // Calculate canvas height based on content
      const canvasHeight = this.calculateCanvasHeight(comment, formatSettings, width);

      // Create canvas
      const canvas = createCanvas(width, canvasHeight);
      const ctx = canvas.getContext('2d');

      // Set background to white
      ctx.fillStyle = 'white';
      ctx.fillRect(0, 0, width, canvasHeight);

      // Render content with custom formatting
      this.renderFormattedContent(ctx, comment, formatSettings, width, padding);

      // Convert canvas to bitmap data for ESC/POS
      const imageBuffer = canvas.toBuffer('image/png');
      const bitmapData = await this.convertImageToBitmap(imageBuffer, width);

      return bitmapData;

    } catch (error) {
      this.logger.error('Failed to generate bitmap:', error);
      throw error;
    }
  }

  getDefaultPrintSettings() {
    return {
      printer: {
        selectedPrinter: '',
        connectionType: 'system',
        printMode: 'bitmap',
        cutType: 'partial'
      },
      singleComment: {
        username: {
          fontSize: 32, fontWeight: 'bold', fontStyle: 'normal',
          fontFamily: 'Arial, sans-serif', textAlign: 'left', marginBottom: 8
        },
        timestamp: {
          fontSize: 20, fontWeight: 'normal', fontStyle: 'normal',
          fontFamily: 'Arial, sans-serif', textAlign: 'left', marginBottom: 10
        },
        content: {
          fontSize: 28, fontWeight: 'normal', fontStyle: 'normal',
          fontFamily: 'Arial, sans-serif', textAlign: 'left', marginBottom: 12
        },
        padding: 12, lineSpacing: 4, showHeader: false, headerText: '', showFooter: false, footerText: ''
      },
      userHistory: {
        header: {
          fontSize: 24, fontWeight: 'bold', fontStyle: 'normal',
          fontFamily: 'Arial, sans-serif', textAlign: 'center', marginBottom: 12
        },
        username: {
          fontSize: 22, fontWeight: 'bold', fontStyle: 'normal',
          fontFamily: 'Arial, sans-serif', textAlign: 'left', marginBottom: 10
        },
        commentItem: {
          fontSize: 20, fontWeight: 'normal', fontStyle: 'normal',
          fontFamily: 'Arial, sans-serif', textAlign: 'left', marginBottom: 8
        },
        timestamp: {
          fontSize: 16, fontWeight: 'normal', fontStyle: 'normal',
          fontFamily: 'Arial, sans-serif', textAlign: 'left', marginBottom: 8
        },
        separator: { show: true, style: '---' },
        padding: 12, lineSpacing: 4, maxCommentsPerPage: 10, showSummary: true
      }
    };
  }

  calculateCanvasHeight(comment, formatSettings, width) {
    // Estimate height based on content and settings with more generous spacing
    let totalHeight = formatSettings.padding * 3; // More padding

    // Username height with extra space
    totalHeight += formatSettings.username.fontSize * 1.5 + formatSettings.username.marginBottom;

    // Timestamp height with extra space
    totalHeight += formatSettings.timestamp.fontSize * 1.5 + formatSettings.timestamp.marginBottom;

    // Content height (more accurate calculation with word wrapping)
    const avgCharWidth = formatSettings.content.fontSize * 0.5; // More conservative estimate
    const usableWidth = width - (formatSettings.padding * 2);
    const charsPerLine = Math.floor(usableWidth / avgCharWidth);
    const contentLines = Math.max(1, Math.ceil(comment.text.length / charsPerLine));
    const lineHeight = formatSettings.content.fontSize * 1.4 + formatSettings.lineSpacing;
    totalHeight += lineHeight * contentLines + formatSettings.content.marginBottom;

    // Header/footer if enabled
    if (formatSettings.showHeader && formatSettings.headerText) {
      totalHeight += 30; // More space for header
    }
    if (formatSettings.showFooter && formatSettings.footerText) {
      totalHeight += 30; // More space for footer
    }

    // Add extra buffer space to prevent cutting
    totalHeight += 40;

    return Math.max(totalHeight, 150); // Higher minimum height
  }

  renderFormattedContent(ctx, comment, formatSettings, width, padding) {
    ctx.fillStyle = 'black';
    ctx.textBaseline = 'top';

    let y = padding;

    // Header
    if (formatSettings.showHeader && formatSettings.headerText) {
      ctx.font = '12px Arial, sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText(formatSettings.headerText, width / 2, y);
      y += 20;
    }

    // Username
    const usernameSettings = formatSettings.username;
    ctx.font = `${usernameSettings.fontStyle} ${usernameSettings.fontWeight} ${usernameSettings.fontSize}px ${usernameSettings.fontFamily}`;
    ctx.textAlign = usernameSettings.textAlign;

    const usernameX = usernameSettings.textAlign === 'center' ? width / 2 :
      usernameSettings.textAlign === 'right' ? width - padding : padding;

    const usernameText = comment.isBackup
      ? `@${comment.username} (dự bị)`
      : `@${comment.username}`;
    ctx.fillText(usernameText, usernameX, y);
    y += usernameSettings.fontSize * 1.3 + usernameSettings.marginBottom;

    // Timestamp
    const timestampSettings = formatSettings.timestamp;
    ctx.font = `${timestampSettings.fontStyle} ${timestampSettings.fontWeight} ${timestampSettings.fontSize}px ${timestampSettings.fontFamily}`;
    ctx.textAlign = timestampSettings.textAlign;

    const timestampX = timestampSettings.textAlign === 'center' ? width / 2 :
      timestampSettings.textAlign === 'right' ? width - padding : padding;
    const timestamp = new Date(comment.timestamp).toLocaleString('vi-VN', {
      timeZone: 'Asia/Ho_Chi_Minh',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
    ctx.fillText(timestamp, timestampX, y);
    y += timestampSettings.fontSize * 1.3 + timestampSettings.marginBottom;

    // Content
    const contentSettings = formatSettings.content;
    ctx.font = `${contentSettings.fontStyle} ${contentSettings.fontWeight} ${contentSettings.fontSize}px ${contentSettings.fontFamily}`;
    ctx.textAlign = contentSettings.textAlign;

    // Word wrap for content
    const maxWidth = width - (padding * 2);
    const words = comment.text.split(' ');
    let line = '';

    for (let i = 0; i < words.length; i++) {
      const testLine = line + words[i] + ' ';
      const metrics = ctx.measureText(testLine);

      if (metrics.width > maxWidth && line !== '') {
        const contentX = contentSettings.textAlign === 'center' ? width / 2 :
          contentSettings.textAlign === 'right' ? width - padding : padding;
        ctx.fillText(line.trim(), contentX, y);
        y += contentSettings.fontSize * 1.4 + formatSettings.lineSpacing;
        line = words[i] + ' ';
      } else {
        line = testLine;
      }
    }

    // Draw remaining text
    if (line.trim()) {
      const contentX = contentSettings.textAlign === 'center' ? width / 2 :
        contentSettings.textAlign === 'right' ? width - padding : padding;
      ctx.fillText(line.trim(), contentX, y);
      y += contentSettings.fontSize * 1.4 + contentSettings.marginBottom;
    }

    // Footer
    if (formatSettings.showFooter && formatSettings.footerText) {
      ctx.font = '12px Arial, sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText(formatSettings.footerText, width / 2, y);
    }
  }

  async convertImageToBitmap(imageBuffer, targetWidth) {
    try {
      // Use sharp to process image
      const { data, info } = await sharp(imageBuffer)
        .resize(targetWidth, null, {
          fit: 'inside',
          withoutEnlargement: false
        })
        .greyscale()
        .threshold(128) // Convert to black/white
        .raw()
        .toBuffer({ resolveWithObject: true });

      const { width, height } = info;

      // Convert to ESC/POS bitmap format
      const ESC = '\x1B';
      const GS = '\x1D';

      let escPosData = Buffer.from([]);

      // Initialize printer
      escPosData = Buffer.concat([escPosData, Buffer.from(ESC + '@')]);

      // Set bitmap mode
      const widthBytes = Math.ceil(width / 8);
      const heightLow = height & 0xFF;
      const heightHigh = (height >> 8) & 0xFF;
      const widthLow = widthBytes & 0xFF;
      const widthHigh = (widthBytes >> 8) & 0xFF;

      // ESC/POS bitmap command: GS v 0 m xL xH yL yH d1...dk
      escPosData = Buffer.concat([
        escPosData,
        Buffer.from([0x1D, 0x76, 0x30, 0x00, widthLow, widthHigh, heightLow, heightHigh])
      ]);

      // Convert pixel data to bitmap bytes
      const bitmapBytes = [];
      for (let y = 0; y < height; y++) {
        for (let x = 0; x < widthBytes; x++) {
          let byte = 0;
          for (let bit = 0; bit < 8; bit++) {
            const pixelX = x * 8 + bit;
            if (pixelX < width) {
              const pixelIndex = (y * width + pixelX);
              const pixelValue = data[pixelIndex];
              // If pixel is black (0), set bit to 1
              if (pixelValue === 0) {
                byte |= (1 << (7 - bit));
              }
            }
          }
          bitmapBytes.push(byte);
        }
      }

      escPosData = Buffer.concat([escPosData, Buffer.from(bitmapBytes)]);

      // Add some line feeds before cutting to ensure content is fully printed
      escPosData = Buffer.concat([escPosData, Buffer.from('\n\n\n')]);

      // Add cut command
      if (this.printerSettings.cutType === 'full') {
        escPosData = Buffer.concat([escPosData, Buffer.from(GS + 'V\x00')]);
      } else if (this.printerSettings.cutType === 'partial') {
        escPosData = Buffer.concat([escPosData, Buffer.from(GS + 'V\x01')]);
      }

      // Open cash drawer if enabled
      if (this.printerSettings.cashdrawer) {
        escPosData = Buffer.concat([escPosData, Buffer.from(ESC + 'p\x00\x19\xFA')]);
      }

      return escPosData;

    } catch (error) {
      this.logger.error('Failed to convert image to bitmap:', error);
      throw error;
    }
  }

  async printBitmapToSystemPrinter(bitmapData, comment) {
    try {
      // For system printer, save bitmap as image file and print
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `bitmap_${comment?.username || 'test'}_${timestamp}.png`;
      const filepath = path.join(__dirname, '../temp', filename);

      // Ensure temp directory exists
      await fs.mkdir(path.dirname(filepath), { recursive: true });

      // Extract image data from ESC/POS bitmap
      // For system printer, regenerate clean image
      const { width, fontSize, fontFamily, lineSpacing, padding } = this.printerSettings.bitmapSettings;
      const textContent = this.formatCommentForPrint(comment, 'comment');
      const lines = textContent.split('\n');
      const lineHeight = fontSize + lineSpacing;
      const canvasHeight = (lines.length * lineHeight) + (padding * 2);

      const canvas = createCanvas(width, canvasHeight);
      const ctx = canvas.getContext('2d');

      ctx.fillStyle = 'white';
      ctx.fillRect(0, 0, width, canvasHeight);
      ctx.fillStyle = 'black';
      ctx.font = `${fontSize}px ${fontFamily}`;
      ctx.textAlign = 'left';
      ctx.textBaseline = 'top';

      let y = padding;
      for (const line of lines) {
        if (line.trim()) {
          if (line.includes('===') || line.includes('---')) {
            ctx.textAlign = 'center';
            ctx.fillText(line, width / 2, y);
            ctx.textAlign = 'left';
          } else {
            ctx.fillText(line, padding, y);
          }
        }
        y += lineHeight;
      }

      // Save as PNG
      const imageBuffer = canvas.toBuffer('image/png');
      await fs.writeFile(filepath, imageBuffer);

      // Print using Windows photo viewer or default image viewer
      const { exec } = require('child_process');
      const { promisify } = require('util');
      const execAsync = promisify(exec);

      // Try to print with default image viewer
      const command = `rundll32.exe shimgvw.dll,ImageView_PrintTo "${filepath}" "${this.printerSettings.selectedPrinter || 'default'}"`;

      try {
        await execAsync(command);
        this.logger.info('Bitmap printed via system printer');
      } catch (error) {
        // Fallback: just open the image
        await execAsync(`start "" "${filepath}"`);
        this.logger.info('Bitmap image opened for manual printing');
      }

      // Clean up after delay
      setTimeout(async () => {
        try {
          await fs.unlink(filepath);
        } catch (error) {
          // Ignore cleanup errors
        }
      }, 30000);

      return { success: true, method: 'system_bitmap', filepath };

    } catch (error) {
      this.logger.error('Failed to print bitmap to system printer:', error);
      throw error;
    }
  }

  async printUserHistory(username, comments) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      // Create user history data
      const historyData = {
        username,
        comments: comments.map(comment => ({
          timestamp: comment.timestamp,
          text: comment.text
        })),
        totalComments: comments.length
      };

      let printData;
      let result;

      if (this.printerSettings.printMode === 'bitmap') {
        // Generate bitmap for user history
        printData = await this.generateUserHistoryBitmap(historyData);
      } else {
        // Traditional text mode
        const formattedContent = this.formatUserHistoryForPrint(historyData);
        printData = this.convertToESCPOS(formattedContent);
      }

      // Print based on connection type
      if (this.printerSettings.connectionType === 'system') {
        result = await this.printUserHistoryToSystemPrinter(printData, historyData);
      } else if (this.printerSettings.connectionType === 'usb') {
        result = await this.printToUSB(printData);
      } else if (this.printerSettings.connectionType === 'network') {
        result = await this.printToNetwork(printData);
      } else if (this.printerSettings.connectionType === 'file') {
        // Print user history to text file
        const formattedContent = this.formatUserHistoryForPrint(historyData);
        result = await this.printUserHistoryToFile(formattedContent, historyData);
      } else {
        throw new Error('Invalid printer connection type');
      }

      this.logger.info(`User history printed successfully: ${username} (${comments.length} comments)`);
      return result;

    } catch (error) {
      this.logger.error('Failed to print user history:', error);
      throw error;
    }
  }

  formatUserHistoryForPrint(historyData) {
    const header = `
=======================================
         LỊCH SỬ BÌNH LUẬN
=======================================
Username: @${historyData.username}
Tổng số bình luận: ${historyData.totalComments}
Ngày in: ${new Date().toLocaleString('vi-VN', {
      timeZone: 'Asia/Ho_Chi_Minh',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })}
=======================================

`;

    let content = header;

    // Add comments
    historyData.comments.forEach((comment, index) => {
      const timestamp = new Date(comment.timestamp).toLocaleString('vi-VN', {
        timeZone: 'Asia/Ho_Chi_Minh',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
      content += `${index + 1}. [${timestamp}]\n`;
      content += `   ${comment.text}\n`;
      if (index < historyData.comments.length - 1) {
        content += `   ${'─'.repeat(35)}\n`;
      }
      content += '\n';
    });

    content += `
=======================================
         KẾT THÚC LỊCH SỬ
=======================================`;

    return content;
  }

  async printUserHistoryToFile(content, historyData) {
    try {
      // Get file settings from print settings
      const fileSettings = this.printSettings?.printer?.fileSettings || {
        outputPath: '',
        fileNameFormat: 'timestamp'
      };

      // Determine output directory
      const outputDir = fileSettings.outputPath || path.join(__dirname, '../../prints');
      await fs.mkdir(outputDir, { recursive: true });

      // Generate filename based on format
      let filename;
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);

      switch (fileSettings.fileNameFormat) {
        case 'username':
          filename = `history_${historyData.username}_${timestamp}.txt`;
          break;
        case 'sequential':
          // Find next sequential number for history files
          const files = await fs.readdir(outputDir);
          const historyFiles = files.filter(f => f.startsWith('history_') && f.endsWith('.txt'));
          const numbers = historyFiles.map(f => {
            const match = f.match(/history_(\d+)\.txt/);
            return match ? parseInt(match[1]) : 0;
          });
          const nextNum = Math.max(0, ...numbers) + 1;
          filename = `history_${nextNum.toString().padStart(3, '0')}.txt`;
          break;
        default: // timestamp
          filename = `history_${timestamp}.txt`;
      }

      const filepath = path.join(outputDir, filename);

      // Write to file
      await fs.writeFile(filepath, content, 'utf8');

      this.logger.info(`User history printed to file: ${filepath}`);
      return {
        success: true,
        method: 'file',
        filepath: filepath,
        filename: filename
      };

    } catch (error) {
      this.logger.error('Failed to print user history to file:', error);
      throw error;
    }
  }

  async generateUserHistoryBitmap(historyData) {
    try {
      // Load print settings if not loaded
      if (!this.printSettings && this.database) {
        this.printSettings = await this.database.getPrintSettings();
      }

      // Use print settings or fallback to defaults
      const allSettings = this.printSettings || this.getDefaultPrintSettings();
      const formatSettings = allSettings.userHistory;
      const paperWidthMm = 80; // Fixed 80mm paper width
      const padding = formatSettings.padding || 8;

      // Convert mm to pixels (203 DPI for HPRT TP80N)
      const width = Math.round((paperWidthMm / 25.4) * 203);

      // Calculate canvas height based on content
      const canvasHeight = this.calculateUserHistoryCanvasHeight(historyData, formatSettings, width);

      // Create canvas
      const canvas = createCanvas(width, canvasHeight);
      const ctx = canvas.getContext('2d');

      // Set background to white
      ctx.fillStyle = 'white';
      ctx.fillRect(0, 0, width, canvasHeight);

      // Render content with custom formatting
      this.renderUserHistoryContent(ctx, historyData, formatSettings, width, padding);

      // Convert canvas to bitmap data for ESC/POS
      const imageBuffer = canvas.toBuffer('image/png');
      const bitmapData = await this.convertImageToBitmap(imageBuffer, width);

      return bitmapData;

    } catch (error) {
      this.logger.error('Failed to generate user history bitmap:', error);
      throw error;
    }
  }

  calculateUserHistoryCanvasHeight(historyData, formatSettings, width) {
    let totalHeight = formatSettings.padding * 2; // Top and bottom padding

    // Header height
    totalHeight += formatSettings.header.fontSize + formatSettings.header.marginBottom;

    // Username height
    totalHeight += formatSettings.username.fontSize + formatSettings.username.marginBottom;

    // Separator
    if (formatSettings.separator.show) {
      totalHeight += 20;
    }

    // Comments height (estimate)
    const maxComments = Math.min(historyData.comments.length, formatSettings.maxCommentsPerPage);
    for (let i = 0; i < maxComments; i++) {
      // Timestamp + content + separator
      totalHeight += formatSettings.timestamp.fontSize + formatSettings.timestamp.marginBottom;

      // Estimate content lines
      const contentLines = Math.ceil(historyData.comments[i].text.length / (width / (formatSettings.commentItem.fontSize * 0.6)));
      totalHeight += (formatSettings.commentItem.fontSize + formatSettings.lineSpacing) * contentLines + formatSettings.commentItem.marginBottom;

      if (formatSettings.separator.show && i < maxComments - 1) {
        totalHeight += 15; // Separator height
      }
    }

    // Summary
    if (formatSettings.showSummary) {
      totalHeight += 30;
    }

    return Math.max(totalHeight, 200); // Minimum height
  }

  renderUserHistoryContent(ctx, historyData, formatSettings, width, padding) {
    ctx.fillStyle = 'black';
    ctx.textBaseline = 'top';

    let y = padding;

    // Header
    const headerSettings = formatSettings.header;
    ctx.font = `${headerSettings.fontStyle} ${headerSettings.fontWeight} ${headerSettings.fontSize}px ${headerSettings.fontFamily}`;
    ctx.textAlign = headerSettings.textAlign;

    const headerX = headerSettings.textAlign === 'center' ? width / 2 :
      headerSettings.textAlign === 'right' ? width - padding : padding;
    ctx.fillText('LỊCH SỬ BÌNH LUẬN', headerX, y);
    y += headerSettings.fontSize + headerSettings.marginBottom;

    // Username
    const usernameSettings = formatSettings.username;
    ctx.font = `${usernameSettings.fontStyle} ${usernameSettings.fontWeight} ${usernameSettings.fontSize}px ${usernameSettings.fontFamily}`;
    ctx.textAlign = usernameSettings.textAlign;

    const usernameX = usernameSettings.textAlign === 'center' ? width / 2 :
      usernameSettings.textAlign === 'right' ? width - padding : padding;
    ctx.fillText(`@${historyData.username}`, usernameX, y);
    y += usernameSettings.fontSize + usernameSettings.marginBottom;

    // Separator
    if (formatSettings.separator.show) {
      ctx.font = '12px Arial, sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText(formatSettings.separator.style, width / 2, y);
      y += 20;
    }

    // Comments
    const maxComments = Math.min(historyData.comments.length, formatSettings.maxCommentsPerPage);
    for (let i = 0; i < maxComments; i++) {
      const comment = historyData.comments[i];

      // Timestamp
      const timestampSettings = formatSettings.timestamp;
      ctx.font = `${timestampSettings.fontStyle} ${timestampSettings.fontWeight} ${timestampSettings.fontSize}px ${timestampSettings.fontFamily}`;
      ctx.textAlign = timestampSettings.textAlign;

      const timestampX = timestampSettings.textAlign === 'center' ? width / 2 :
        timestampSettings.textAlign === 'right' ? width - padding : padding;
      const timestamp = new Date(comment.timestamp).toLocaleString('vi-VN', {
        timeZone: 'Asia/Ho_Chi_Minh',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
      ctx.fillText(timestamp, timestampX, y);
      y += timestampSettings.fontSize + timestampSettings.marginBottom;

      // Comment content
      const contentSettings = formatSettings.commentItem;
      ctx.font = `${contentSettings.fontStyle} ${contentSettings.fontWeight} ${contentSettings.fontSize}px ${contentSettings.fontFamily}`;
      ctx.textAlign = contentSettings.textAlign;

      // Word wrap for content
      const maxWidth = width - (padding * 2);
      const words = comment.text.split(' ');
      let line = '';

      for (let j = 0; j < words.length; j++) {
        const testLine = line + words[j] + ' ';
        const metrics = ctx.measureText(testLine);

        if (metrics.width > maxWidth && line !== '') {
          const contentX = contentSettings.textAlign === 'center' ? width / 2 :
            contentSettings.textAlign === 'right' ? width - padding : padding;
          ctx.fillText(line.trim(), contentX, y);
          y += contentSettings.fontSize + formatSettings.lineSpacing;
          line = words[j] + ' ';
        } else {
          line = testLine;
        }
      }

      // Draw remaining text
      if (line.trim()) {
        const contentX = contentSettings.textAlign === 'center' ? width / 2 :
          contentSettings.textAlign === 'right' ? width - padding : padding;
        ctx.fillText(line.trim(), contentX, y);
        y += contentSettings.fontSize + contentSettings.marginBottom;
      }

      // Separator between comments
      if (formatSettings.separator.show && i < maxComments - 1) {
        ctx.font = '10px Arial, sans-serif';
        ctx.textAlign = 'center';
        ctx.fillStyle = '#ccc';
        ctx.fillText(formatSettings.separator.style, width / 2, y);
        ctx.fillStyle = 'black';
        y += 15;
      }
    }

    // Summary
    if (formatSettings.showSummary) {
      ctx.font = '12px Arial, sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText(`Tổng: ${historyData.totalComments} bình luận`, width / 2, y);
    }
  }

  async printUserHistoryToSystemPrinter(bitmapData, historyData) {
    try {
      // For system printer, save bitmap as image file and print
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `history_${historyData.username}_${timestamp}.png`;
      const filepath = path.join(__dirname, '../temp', filename);

      // Ensure temp directory exists
      await fs.mkdir(path.dirname(filepath), { recursive: true });

      // Regenerate clean image for system printer
      const allSettings = this.printSettings || this.getDefaultPrintSettings();
      const formatSettings = allSettings.userHistory;
      const paperWidthMm = 80; // Fixed 80mm paper width
      const width = Math.round((paperWidthMm / 25.4) * 203);
      const canvasHeight = this.calculateUserHistoryCanvasHeight(historyData, formatSettings, width);

      const canvas = createCanvas(width, canvasHeight);
      const ctx = canvas.getContext('2d');

      ctx.fillStyle = 'white';
      ctx.fillRect(0, 0, width, canvasHeight);

      this.renderUserHistoryContent(ctx, historyData, formatSettings, width, formatSettings.padding);

      // Save as PNG
      const imageBuffer = canvas.toBuffer('image/png');
      await fs.writeFile(filepath, imageBuffer);

      // Print using Windows photo viewer or default image viewer
      const { exec } = require('child_process');
      const { promisify } = require('util');
      const execAsync = promisify(exec);

      // Try to print with default image viewer
      const command = `rundll32.exe shimgvw.dll,ImageView_PrintTo "${filepath}" "${this.printerSettings.selectedPrinter || 'default'}"`;

      try {
        await execAsync(command);
        this.logger.info('User history bitmap printed via system printer');
      } catch (error) {
        // Fallback: just open the image
        await execAsync(`start "" "${filepath}"`);
        this.logger.info('User history bitmap image opened for manual printing');
      }

      // Clean up after delay
      setTimeout(async () => {
        try {
          await fs.unlink(filepath);
        } catch (error) {
          // Ignore cleanup errors
        }
      }, 30000);

      return { success: true, method: 'system_bitmap', filepath };

    } catch (error) {
      this.logger.error('Failed to print user history to system printer:', error);
      throw error;
    }
  }

  async testPrint() {
    const testComment = {
      id: 'test-' + Date.now(),
      username: 'test_user',
      text: 'Đây là bản in thử nghiệm cho máy in HPRT TP80N. Test tiếng Việt có dấu: áàảãạăắằẳẵặâấầẩẫậéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵđ',
      timestamp: new Date().toLocaleString('sv-SE', {
        timeZone: 'Asia/Ho_Chi_Minh'
      }),
      price: '50,000',
      total: '50,000'
    };

    return await this.printComment(testComment, 'receipt');
  }
}

module.exports = PrinterService;

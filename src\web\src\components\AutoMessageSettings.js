import React, { useState, useEffect } from 'react';
import { MessageSquare, Save, RotateCw } from 'lucide-react';
import { getApiUrl } from '../config/api';
import toast from 'react-hot-toast';

const AutoMessageSettings = () => {
  const [settings, setSettings] = useState({
    maxMessagesBeforeRestart: 10
  });
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const response = await fetch(getApiUrl('/api/auto-message-settings'));
      const data = await response.json();
      if (data.success) {
        setSettings(prev => ({
          ...prev,
          maxMessagesBeforeRestart: data.settings.maxMessagesBeforeRestart || 10
        }));
      }
    } catch (error) {
      console.error('Failed to load auto message settings:', error);
    }
  };

  const saveSettings = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(getApiUrl('/api/auto-message-settings'));
      const data = await response.json();

      if (data.success) {
        const currentSettings = data.settings;

        // Cập nhật chỉ trường maxMessagesBeforeRestart
        const updatedSettings = {
          ...currentSettings,
          maxMessagesBeforeRestart: settings.maxMessagesBeforeRestart
        };

        const saveResponse = await fetch(getApiUrl('/api/auto-message-settings'), {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ settings: updatedSettings }),
        });

        const saveData = await saveResponse.json();
        if (saveData.success) {
          toast.success('Cài đặt đã được lưu thành công!');
        } else {
          throw new Error(saveData.error || 'Lỗi khi lưu cài đặt');
        }
      }
    } catch (error) {
      toast.error('Lỗi: ' + error.message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm">
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center">
          <MessageSquare className="h-6 w-6 text-blue-600 mr-3" />
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Cài đặt Auto Message</h3>
            <p className="text-sm text-gray-600">Cấu hình hệ thống gửi tin nhắn tự động</p>
          </div>
        </div>
      </div>

      <div className="p-6">
        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Tự động khởi động lại sau số tin nhắn
            </label>
            <div className="flex items-center">
              <input
                type="number"
                min="1"
                max="1000"
                value={settings.maxMessagesBeforeRestart}
                onChange={(e) => setSettings(prev => ({ ...prev, maxMessagesBeforeRestart: parseInt(e.target.value) }))}
                className="w-24 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
              <div className="ml-3 text-sm text-gray-600">tin nhắn</div>
            </div>
            <p className="mt-2 text-sm text-gray-500">
              <RotateCw className="inline h-4 w-4 mr-1" />
              Hệ thống sẽ tự động dừng và khởi động lại sau khi gửi đủ số tin nhắn này để đảm bảo hiệu suất tốt nhất.
            </p>
          </div>

          <div className="pt-4 border-t border-gray-200">
            <button
              onClick={saveSettings}
              disabled={isLoading}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {isLoading ? (
                <>
                  <span className="inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></span>
                  Đang lưu...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Lưu cài đặt
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AutoMessageSettings;

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS for the Instagram Live Comment System */

/* Base styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f8fafc;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Hide scrollbar on mobile */
@media (max-width: 768px) {
  ::-webkit-scrollbar {
    display: none;
  }

  * {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
}

/* Custom components */
.card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-4;
}

.btn-primary {
  @apply bg-sky-500 hover:bg-sky-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:ring-offset-2;
}

.btn-secondary {
  @apply bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
}

.btn-success {
  @apply bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2;
}

.btn-danger {
  @apply bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2;
}

.btn-warning {
  @apply bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2;
}

/* Button size variants */
.btn-sm {
  @apply py-1.5 px-3 text-sm;
}

.btn-xs {
  @apply py-1 px-2 text-xs;
}

.input-field {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-transparent transition-all duration-200;
}

.textarea-field {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-transparent transition-all duration-200 resize-none;
}

/* Input and form element aliases */
.input {
  @apply input-field;
}

.checkbox {
  @apply h-4 w-4 text-sky-600 focus:ring-sky-500 border-gray-300 rounded;
}

/* Status indicators */
.status-online {
  @apply bg-green-500;
}

.status-offline {
  @apply bg-red-500;
}

.status-warning {
  @apply bg-yellow-500;
}

.status-indicator {
  @apply w-3 h-3 rounded-full inline-block;
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

.bounce-in {
  animation: bounceIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }

  50% {
    transform: scale(1.05);
  }

  70% {
    transform: scale(0.9);
  }

  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Loading spinner */
.spinner {
  border: 3px solid #f3f3f3;
  border-top: 3px solid #8b5cf6;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* Comment card styles */
.comment-card {
  @apply bg-white rounded-lg border border-gray-200 p-4 mb-3 shadow-sm hover:shadow-md transition-shadow duration-200;
}

.comment-card.new {
  @apply border-sky-300 bg-sky-50;
  animation: pulse 2s ease-in-out;
}

.comment-card.processed {
  @apply border-green-300 bg-green-50;
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.8;
  }
}

/* Metric card styles */
.metric-card {
  @apply bg-gradient-to-br from-sky-500 to-sky-600 text-white rounded-lg p-4 text-center;
}

.metric-value {
  @apply text-2xl font-bold mb-1;
}

.metric-label {
  @apply text-sm opacity-90;
}

/* Mobile optimizations - iPhone 12 Pro and larger (390px+) */
@media (max-width: 640px) {
  .card {
    @apply p-3;
  }

  .btn-primary,
  .btn-secondary,
  .btn-success,
  .btn-danger,
  .btn-warning {
    @apply py-3 px-4 text-sm;
  }

  .btn-sm {
    @apply py-2 px-3 text-sm;
  }

  .btn-xs {
    @apply py-1.5 px-2 text-xs;
  }

  .metric-value {
    @apply text-xl;
  }

  .comment-card {
    @apply p-3 mb-2;
  }

  /* Better spacing for mobile forms */
  .input-field,
  .textarea-field,
  .input {
    @apply text-base; /* Prevent zoom on iOS */
  }
}

/* Extra small mobile optimizations - iPhone 12 Pro (390px) */
@media (max-width: 420px) {
  .card {
    @apply p-2;
  }

  .btn-primary,
  .btn-secondary,
  .btn-success,
  .btn-danger,
  .btn-warning {
    @apply py-2.5 px-3 text-sm;
  }

  .btn-sm {
    @apply py-2 px-2.5 text-xs;
  }

  .btn-xs {
    @apply py-1 px-2 text-xs;
  }

  .comment-card {
    @apply p-2 mb-2;
  }

  /* Compact spacing for very small screens - use specific classes instead of overriding HTML tags */
  .mobile-h1 {
    @apply text-xl;
  }

  .mobile-h2 {
    @apply text-lg;
  }

  .mobile-h3 {
    @apply text-base;
  }

  /* Reduce padding in containers */
  .container {
    @apply px-2;
  }
}

/* Safe area for notched devices */
.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

/* Touch-friendly buttons */
@media (hover: none) and (pointer: coarse) {

  .btn-primary,
  .btn-secondary,
  .btn-success,
  .btn-danger,
  .btn-warning {
    @apply min-h-[44px] min-w-[44px];
  }

  .btn-sm {
    @apply min-h-[40px] min-w-[40px];
  }

  .btn-xs {
    @apply min-h-[36px] min-w-[36px];
  }
}

/* iPhone 12 Pro specific optimizations (390px) */
@media (max-width: 390px) {
  .card {
    @apply p-1.5;
  }

  .btn-primary,
  .btn-secondary,
  .btn-success,
  .btn-danger,
  .btn-warning {
    @apply py-2 px-2.5 text-xs;
  }

  .btn-sm {
    @apply py-1.5 px-2 text-xs;
  }

  .btn-xs {
    @apply py-1 px-1.5 text-xs;
  }

  /* Ultra compact spacing */
  .comment-card {
    @apply p-1.5 mb-1.5;
  }

  /* Smaller text for very small screens - use specific classes instead of overriding Tailwind */
  .mobile-text-sm {
    @apply text-xs;
  }

  /* Reduce header height - use specific classes instead of overriding Tailwind */
  .mobile-h-16 {
    @apply h-12;
  }

  .mobile-h-14 {
    @apply h-12;
  }

  /* Compact form elements */
  .input-field,
  .textarea-field,
  .input {
    @apply py-1.5 px-2 text-sm;
  }
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
  /* Dark mode styles can be added here */
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  .print-only {
    display: block !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .card {
    @apply border-2 border-gray-800;
  }

  .btn-primary {
    @apply border-2 border-sky-800;
  }
}

/* Toast container styles */
.toast-container {
  max-height: 200px;
  overflow: hidden;
}

/* Limit number of visible toasts */
.toast-container>div:nth-child(n+4) {
  display: none !important;
}

/* Toast animation improvements */
.toast-container>div {
  transition: all 0.3s ease-in-out;
  margin-bottom: 8px;
}

/* Mobile toast adjustments */
@media (max-width: 640px) {
  .toast-container {
    max-height: 150px;
  }

  .toast-container>div:nth-child(n+3) {
    display: none !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
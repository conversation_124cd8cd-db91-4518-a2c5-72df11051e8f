@echo off
echo ================================================
echo Debug Message Queue API Endpoints
echo ================================================
cd /d "%~dp0"

echo Testing /api/health endpoint...
curl http://localhost:3001/api/health

echo.
echo Testing /api/message-queue/next endpoint...
curl http://localhost:3001/api/message-queue/next

echo.
echo ================================================
echo.
echo Creating direct API endpoint for testing...

echo const express = require('express'); > temp-api-server.js
echo const app = express(); >> temp-api-server.js
echo const cors = require('cors'); >> temp-api-server.js
echo. >> temp-api-server.js
echo app.use(cors()); >> temp-api-server.js
echo app.use(express.json()); >> temp-api-server.js
echo. >> temp-api-server.js
echo // Test endpoint >> temp-api-server.js
echo app.get('/api/message-queue/next', (req, res) => { >> temp-api-server.js
echo   res.json({ >> temp-api-server.js
echo     success: true, >> temp-api-server.js
echo     message: { >> temp-api-server.js
echo       id: 'test-123', >> temp-api-server.js
echo       username: 'test_user', >> temp-api-server.js
echo       message: 'This is a test message', >> temp-api-server.js
echo       customer_type: 'regular' >> temp-api-server.js
echo     } >> temp-api-server.js
echo   }); >> temp-api-server.js
echo }); >> temp-api-server.js
echo. >> temp-api-server.js
echo app.listen(3002, () => { >> temp-api-server.js
echo   console.log('Test API server running on port 3002'); >> temp-api-server.js
echo }); >> temp-api-server.js

echo.
echo Starting test API server on port 3002...
start cmd /k "node temp-api-server.js"

echo.
echo Waiting for test server to start...
timeout /t 3 /nobreak > nul

echo.
echo Testing test server endpoint...
curl http://localhost:3002/api/message-queue/next

echo.
echo.
echo ================================================
echo Now update the NODE_SERVER_URL in InstagrapiService.py to:
echo NODE_SERVER_URL = "http://localhost:3002"
echo ================================================
echo.
echo Press any key to exit...
pause > nul 
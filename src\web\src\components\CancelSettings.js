import React, { useState, useEffect } from 'react';
import { Settings, RotateCcw, Clock, ToggleLeft, ToggleRight } from 'lucide-react';
import { useCancelSettings } from '../contexts/CancelSettingsContext';
import { useSocket } from '../contexts/SocketContext';
import toast from 'react-hot-toast';

const CancelSettings = () => {
  const { settings, updateSettings, resetToDefaults, refreshSettings } = useCancelSettings();
  const { socket, isConnected } = useSocket();
  const [isOpen, setIsOpen] = useState(false);

  // Listen for settings updates from other clients
  useEffect(() => {
    if (socket && isConnected) {
      const handleSettingsUpdate = (data) => {
        if (data.settings) {
          updateSettings(data.settings);
          console.log('Cancel settings updated from server:', data.settings);
        }
      };

      socket.on('cancel-settings-updated', handleSettingsUpdate);

      return () => {
        socket.off('cancel-settings-updated', handleSettingsUpdate);
      };
    }
  }, [socket, isConnected, updateSettings]);

  const handleDurationChange = (value) => {
    const duration = Math.max(500, Math.min(5000, parseInt(value) || 1500));
    updateSettings({ duration });
  };

  const handleToggle = (key) => {
    updateSettings({ [key]: !settings[key] });
  };

  const handleReset = () => {
    resetToDefaults();
    toast.success('Đã khôi phục cài đặt mặc định');
  };

  const formatDuration = (ms) => {
    return (ms / 1000).toFixed(1) + 's';
  };

  return (
    <div className="relative">
      {/* Settings Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 bg-gray-600 hover:bg-gray-700 text-white px-3 py-2 rounded transition-colors"
        title="Cài đặt nút hủy"
      >
        <Settings className="h-4 w-4" />
        <span className="hidden sm:inline">Cài đặt hủy</span>
      </button>

      {/* Settings Panel */}
      {isOpen && (
        <div className="absolute top-full right-0 mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
          <div className="p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">
                Cài đặt nút hủy
              </h3>
              <button
                onClick={handleReset}
                className="flex items-center space-x-1 text-sm text-blue-600 hover:text-blue-700"
                title="Khôi phục mặc định"
              >
                <RotateCcw className="h-4 w-4" />
                <span>Reset</span>
              </button>
            </div>

            {/* Enable/Disable Cancel */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-medium text-gray-700">
                    Bật tính năng hủy
                  </span>
                </div>
                <button
                  onClick={() => handleToggle('enabled')}
                  className={`flex items-center ${settings.enabled ? 'text-green-600' : 'text-gray-400'}`}
                >
                  {settings.enabled ? (
                    <ToggleRight className="h-6 w-6" />
                  ) : (
                    <ToggleLeft className="h-6 w-6" />
                  )}
                </button>
              </div>

              {/* Duration Setting */}
              {settings.enabled && (
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">
                    Thời gian hủy: {formatDuration(settings.duration)}
                  </label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="range"
                      min="500"
                      max="5000"
                      step="100"
                      value={settings.duration}
                      onChange={(e) => handleDurationChange(e.target.value)}
                      className="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                    />
                    <input
                      type="number"
                      min="500"
                      max="5000"
                      step="100"
                      value={settings.duration}
                      onChange={(e) => handleDurationChange(e.target.value)}
                      className="w-16 px-2 py-1 text-sm border border-gray-300 rounded"
                    />
                  </div>
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>0.5s</span>
                    <span>5.0s</span>
                  </div>
                </div>
              )}

              {/* Enable for Print */}
              {settings.enabled && (
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">
                    Bật hủy cho nút In
                  </span>
                  <button
                    onClick={() => handleToggle('enableForPrint')}
                    className={`flex items-center ${settings.enableForPrint ? 'text-green-600' : 'text-gray-400'}`}
                  >
                    {settings.enableForPrint ? (
                      <ToggleRight className="h-5 w-5" />
                    ) : (
                      <ToggleLeft className="h-5 w-5" />
                    )}
                  </button>
                </div>
              )}

              {/* Enable for Backup */}
              {settings.enabled && (
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">
                    Bật hủy cho nút Dự bị
                  </span>
                  <button
                    onClick={() => handleToggle('enableForBackup')}
                    className={`flex items-center ${settings.enableForBackup ? 'text-green-600' : 'text-gray-400'}`}
                  >
                    {settings.enableForBackup ? (
                      <ToggleRight className="h-5 w-5" />
                    ) : (
                      <ToggleLeft className="h-5 w-5" />
                    )}
                  </button>
                </div>
              )}

              {/* Preview */}
              {settings.enabled && (
                <div className="mt-4 p-3 bg-gray-50 rounded">
                  <p className="text-xs text-gray-600 mb-2">
                    Xem trước:
                  </p>
                  <div className="text-sm text-gray-700">
                    • Thời gian hủy: <strong>{formatDuration(settings.duration)}</strong>
                    <br />
                    • Nút In: <strong>{settings.enableForPrint ? 'Bật' : 'Tắt'}</strong>
                    <br />
                    • Nút Dự bị: <strong>{settings.enableForBackup ? 'Bật' : 'Tắt'}</strong>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Overlay to close panel */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
};

export default CancelSettings;

@echo off
echo ========================================
echo   BUILDING INSTAGRAM LIVE COMMENT SYSTEM
echo ========================================

echo.
echo [1/3] Building web production...
call npm run build:web
if %errorlevel% neq 0 (
    echo ERROR: Web build failed!
    pause
    exit /b 1
)

echo.
echo [2/3] Packaging Electron app...
call npm run package
if %errorlevel% neq 0 (
    echo ERROR: Electron packaging failed!
    pause
    exit /b 1
)

echo.
echo [3/3] Creating release package...
cd dist
if exist "Instagram-Live-System-Release.zip" del "Instagram-Live-System-Release.zip"
powershell -Command "Compress-Archive -Path 'instagram-live-system-win32-x64', 'Create-Desktop-Shortcut.bat', 'README-KHACH-HANG.txt' -DestinationPath 'Instagram-Live-System-Release.zip'"

echo.
echo ========================================
echo   BUILD COMPLETED SUCCESSFULLY!
echo ========================================
echo.
echo Release package: dist\Instagram-Live-System-Release.zip
echo App executable: dist\instagram-live-system-win32-x64\instagram-live-system.exe
echo.
echo Ready to distribute to customers!
pause

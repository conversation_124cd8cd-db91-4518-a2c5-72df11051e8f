import React, { useState, useEffect } from 'react';
import { Users, Plus, Trash2, Search, Star, Calendar, Edit3, Save, X, ChevronLeft, ChevronRight } from 'lucide-react';
import { getApiUrl } from '../config/api';
import SyncStatusIndicator from '../components/SyncStatusIndicator';
import SyncButton from '../components/SyncButton';
import toast from 'react-hot-toast';

const CustomerManagement = () => {
  const [customers, setCustomers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState(null);
  const [newCustomer, setNewCustomer] = useState({ username: '', notes: '' });

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Load customers from API
  const loadCustomers = async () => {
    try {
      setLoading(true);
      const response = await fetch(getApiUrl('/api/regular-customers'));
      const data = await response.json();

      if (data.success) {
        setCustomers(data.customers);
      } else {
        toast.error('Lỗi khi tải danh sách khách hàng');
      }
    } catch (error) {
      console.error('Failed to load customers:', error);
      toast.error('Lỗi khi tải danh sách khách hàng');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadCustomers();
  }, []);

  // Add new customer
  const handleAddCustomer = async (e) => {
    e.preventDefault();

    if (!newCustomer.username.trim()) {
      toast.error('Vui lòng nhập tên người dùng');
      return;
    }

    try {
      const response = await fetch(getApiUrl('/api/regular-customers'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          username: newCustomer.username.trim().replace('@', ''),
          notes: newCustomer.notes.trim()
        })
      });

      const data = await response.json();

      if (data.success) {
        toast.success(`Đã thêm khách quen: @${newCustomer.username}`);
        setNewCustomer({ username: '', notes: '' });
        setShowAddForm(false);
        loadCustomers(); // Reload list
      } else {
        toast.error(data.error || 'Lỗi khi thêm khách hàng');
      }
    } catch (error) {
      console.error('Failed to add customer:', error);
      toast.error('Lỗi khi thêm khách hàng');
    }
  };

  // Remove customer
  const handleRemoveCustomer = async (username) => {
    if (!window.confirm(`Bạn có chắc muốn xóa khách quen @${username}?`)) {
      return;
    }

    try {
      const response = await fetch(getApiUrl(`/api/regular-customers/${username}`), {
        method: 'DELETE'
      });

      const data = await response.json();

      if (data.success) {
        toast.success(`Đã xóa khách quen: @${username}`);
        loadCustomers(); // Reload list
      } else {
        toast.error(data.error || 'Lỗi khi xóa khách hàng');
      }
    } catch (error) {
      console.error('Failed to remove customer:', error);
      toast.error('Lỗi khi xóa khách hàng');
    }
  };

  // Update customer notes
  const handleUpdateNotes = async (username, newNotes) => {
    try {
      const response = await fetch(getApiUrl('/api/regular-customers'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          username: username,
          notes: newNotes.trim()
        })
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Đã cập nhật ghi chú');
        setEditingCustomer(null);
        loadCustomers(); // Reload list
      } else {
        toast.error(data.error || 'Lỗi khi cập nhật ghi chú');
      }
    } catch (error) {
      console.error('Failed to update notes:', error);
      toast.error('Lỗi khi cập nhật ghi chú');
    }
  };

  // Filter customers based on search term
  const filteredCustomers = customers.filter(customer =>
    customer.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (customer.notes && customer.notes.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Pagination calculations
  const totalPages = Math.ceil(filteredCustomers.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedCustomers = filteredCustomers.slice(startIndex, endIndex);

  // Reset to first page when search term changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // Pagination handlers
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (newItemsPerPage) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page
  };

  // Format date
  const formatDate = (dateString) => {
    try {
      return new Date(dateString).toLocaleString('vi-VN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      return dateString;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Đang tải...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Users className="h-8 w-8 text-blue-600" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Quản lý khách hàng</h1>
            <p className="text-gray-600">Quản lý danh sách khách hàng thường xuyên</p>
          </div>
          {/* MongoDB Sync Status & Button */}
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2 px-3 py-2 bg-gray-50 rounded-lg border">
              <SyncStatusIndicator
                type="customers"
                size="sm"
                showText={true}
              />
            </div>
            <SyncButton
              type="customers"
              size="sm"
              showText={true}
              onSyncComplete={() => {
                // Reload customers after sync
                loadCustomers();
              }}
            />
          </div>
        </div>
        <button
          onClick={() => setShowAddForm(true)}
          className="btn-primary flex items-center space-x-2"
        >
          <Plus className="h-4 w-4" />
          <span>Thêm khách hàng</span>
        </button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Users className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Tổng khách quen</p>
              <p className="text-2xl font-bold text-gray-900">{customers.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <Star className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Có ghi chú</p>
              <p className="text-2xl font-bold text-gray-900">
                {customers.filter(c => c.notes && c.notes.trim()).length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Calendar className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Thêm hôm nay</p>
              <p className="text-2xl font-bold text-gray-900">
                {customers.filter(c => {
                  const today = new Date().toDateString();
                  const customerDate = new Date(c.marked_at).toDateString();
                  return today === customerDate;
                }).length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Search */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Tìm kiếm theo tên hoặc ghi chú..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Add Customer Form */}
      {showAddForm && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Thêm khách hàng mới</h3>
          <form onSubmit={handleAddCustomer} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Tên người dùng Instagram
              </label>
              <input
                type="text"
                value={newCustomer.username}
                onChange={(e) => setNewCustomer(prev => ({ ...prev, username: e.target.value }))}
                placeholder="Nhập username (không cần @)"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Ghi chú (tùy chọn)
              </label>
              <textarea
                value={newCustomer.notes}
                onChange={(e) => setNewCustomer(prev => ({ ...prev, notes: e.target.value }))}
                placeholder="Ghi chú về khách hàng..."
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div className="flex space-x-3">
              <button type="submit" className="btn-primary">
                Thêm khách hàng
              </button>
              <button
                type="button"
                onClick={() => {
                  setShowAddForm(false);
                  setNewCustomer({ username: '', notes: '' });
                }}
                className="btn-secondary"
              >
                Hủy
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Customer List */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">
            Danh sách khách hàng ({filteredCustomers.length})
          </h3>

          {/* Items per page selector */}
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">Hiển thị:</span>
            <select
              value={itemsPerPage}
              onChange={(e) => handleItemsPerPageChange(Number(e.target.value))}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value={5}>5</option>
              <option value={10}>10</option>
              <option value={20}>20</option>
              <option value={50}>50</option>
              <option value={100}>100</option>
            </select>
            <span className="text-sm text-gray-600">mục/trang</span>
          </div>
        </div>

        {filteredCustomers.length === 0 ? (
          <div className="p-8 text-center">
            <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">
              {searchTerm ? 'Không tìm thấy khách hàng nào' : 'Chưa có khách hàng nào'}
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {paginatedCustomers.map((customer) => (
              <div key={customer.username} className="p-6 hover:bg-gray-50">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <Star className="h-5 w-5 text-yellow-500 fill-current" />
                      <span className="font-semibold text-gray-900">@{customer.username}</span>
                      <span className="text-sm text-gray-500">
                        {formatDate(customer.marked_at)}
                      </span>
                      {/* Individual customer sync status */}
                      {customer.synced_at && (
                        <div className="flex items-center space-x-1 px-2 py-1 bg-green-50 rounded-full">
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                          <span className="text-xs text-green-700">Đã sync</span>
                        </div>
                      )}
                    </div>

                    {editingCustomer && editingCustomer.username === customer.username ? (
                      <div className="mt-2">
                        <textarea
                          value={editingCustomer.notes || ''}
                          onChange={(e) => setEditingCustomer({ ...editingCustomer, notes: e.target.value })}
                          placeholder="Ghi chú về khách hàng..."
                          rows={2}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                        <div className="flex space-x-2 mt-2">
                          <button
                            onClick={() => handleUpdateNotes(customer.username, editingCustomer.notes)}
                            className="btn-primary btn-sm flex items-center space-x-1"
                          >
                            <Save className="h-3 w-3" />
                            <span>Lưu</span>
                          </button>
                          <button
                            onClick={() => setEditingCustomer(null)}
                            className="btn-secondary btn-sm flex items-center space-x-1"
                          >
                            <X className="h-3 w-3" />
                            <span>Hủy</span>
                          </button>
                        </div>
                      </div>
                    ) : (
                      <div className="mt-2">
                        {customer.notes ? (
                          <p className="text-gray-600 text-sm">{customer.notes}</p>
                        ) : (
                          <p className="text-gray-400 text-sm italic">Chưa có ghi chú</p>
                        )}
                      </div>
                    )}
                  </div>

                  <div className="flex items-center space-x-2 ml-4">
                    <button
                      onClick={() => setEditingCustomer({ username: customer.username, notes: customer.notes || '' })}
                      className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                      title="Chỉnh sửa ghi chú"
                    >
                      <Edit3 className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleRemoveCustomer(customer.username)}
                      className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                      title="Xóa khách hàng"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Pagination */}
        {filteredCustomers.length > 0 && totalPages > 1 && (
          <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Hiển thị {startIndex + 1} - {Math.min(endIndex, filteredCustomers.length)} trong tổng số {filteredCustomers.length} khách hàng
            </div>

            <div className="flex items-center space-x-2">
              {/* Previous button */}
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className={`p-2 rounded-md ${
                  currentPage === 1
                    ? 'text-gray-400 cursor-not-allowed'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
              >
                <ChevronLeft className="h-5 w-5" />
              </button>

              {/* Page numbers */}
              <div className="flex items-center space-x-1">
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => {
                  // Show first page, last page, current page, and pages around current page
                  const showPage =
                    page === 1 ||
                    page === totalPages ||
                    (page >= currentPage - 2 && page <= currentPage + 2);

                  if (!showPage) {
                    // Show ellipsis for gaps
                    if (page === currentPage - 3 || page === currentPage + 3) {
                      return (
                        <span key={page} className="px-2 py-1 text-gray-500">
                          ...
                        </span>
                      );
                    }
                    return null;
                  }

                  return (
                    <button
                      key={page}
                      onClick={() => handlePageChange(page)}
                      className={`px-3 py-1 rounded-md text-sm ${
                        page === currentPage
                          ? 'bg-blue-600 text-white'
                          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                      }`}
                    >
                      {page}
                    </button>
                  );
                })}
              </div>

              {/* Next button */}
              <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className={`p-2 rounded-md ${
                  currentPage === totalPages
                    ? 'text-gray-400 cursor-not-allowed'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
              >
                <ChevronRight className="h-5 w-5" />
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CustomerManagement;
